#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型效率评估器启动脚本
Model Efficiency Evaluator Launcher

使用方法:
1. 确保已安装所有依赖包
2. 在项目根目录下运行此脚本
3. 在GUI界面中选择模型和参数进行评估

Author: AI Assistant
Date: 2024
"""

import sys
import os
import subprocess
import importlib.util

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'torch',
        'torchvision', 
        'numpy',
        'psutil',
        'tkinter',
        'PIL',
        'sklearn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image
            elif package == 'sklearn':
                import sklearn
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请使用以下命令安装:")
        if 'tkinter' in missing_packages:
            print("  sudo apt-get install python3-tk  # Ubuntu/Debian")
            print("  或者使用conda: conda install tk")
        
        pip_packages = [p for p in missing_packages if p != 'tkinter']
        if pip_packages:
            print(f"  pip install {' '.join(pip_packages)}")
        
        return False
    
    return True

def check_project_structure():
    """检查项目结构"""
    required_files = [
        'network/__init__.py',
        'network/modeling.py',
        'datasets/__init__.py',
        'utils/__init__.py',
        'backboned_unet/__init__.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("缺少以下项目文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\n请确保在项目根目录下运行此脚本")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("火星地形分割模型效率评估器")
    print("Mars Terrain Segmentation Model Efficiency Evaluator")
    print("=" * 60)
    print()
    
    # 检查依赖
    print("正在检查依赖包...")
    if not check_dependencies():
        print("❌ 依赖检查失败")
        return 1
    print("✅ 依赖检查通过")
    
    # 检查项目结构
    print("正在检查项目结构...")
    if not check_project_structure():
        print("❌ 项目结构检查失败")
        return 1
    print("✅ 项目结构检查通过")
    
    # 启动GUI
    print("正在启动GUI界面...")
    try:
        from model_efficiency_evaluator import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        print("\n请检查以下问题:")
        print("1. 是否在项目根目录下运行")
        print("2. 是否安装了所有依赖包")
        print("3. 项目文件是否完整")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
