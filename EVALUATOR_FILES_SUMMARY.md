# 模型效率评估器文件说明

## 文件概览

我为您创建了一套完整的模型效率评估工具，包含以下文件：

### 核心文件

#### 1. `model_efficiency_evaluator.py` - 主GUI评估器
- **功能**: 图形界面的模型效率评估工具
- **特点**: 
  - 支持下拉菜单选择模型、数据集、输入尺寸、批次大小
  - 实时进度条显示评估进度
  - 详细的硬件环境信息显示
  - 完整的评估报告生成
- **使用**: `python model_efficiency_evaluator.py`

#### 2. `flops_calculator.py` - 专业FLOPs计算器
- **功能**: 精确计算深度学习模型的浮点运算数
- **特点**:
  - 支持Conv2d, ConvTranspose2d, BatchNorm2d, ReLU, Pooling, Linear等层
  - 使用前向钩子函数精确统计
  - 可独立使用或集成到其他工具中
- **使用**: 可作为模块导入使用

#### 3. `batch_evaluator.py` - 批量评估器
- **功能**: 命令行批量评估多个模型
- **特点**:
  - 支持评估所有可用模型或指定模型列表
  - 自动生成JSON、CSV和Markdown格式报告
  - 包含效率分析和模型排名
- **使用**: `python batch_evaluator.py [参数]`

### 辅助文件

#### 4. `run_evaluator.py` - 启动脚本
- **功能**: 检查环境并启动GUI评估器
- **特点**:
  - 自动检查依赖包和项目结构
  - 提供友好的错误提示和解决方案
  - 一键启动评估器
- **使用**: `python run_evaluator.py`

#### 5. `test_evaluator.py` - 测试脚本
- **功能**: 测试评估器的各项功能
- **特点**:
  - 全面测试项目结构、模型创建、参数计算等
  - 提供详细的测试报告
  - 帮助诊断环境问题
- **使用**: `python test_evaluator.py`

#### 6. `example_usage.py` - 使用示例
- **功能**: 展示评估器的各种使用方法
- **特点**:
  - 包含单个模型评估、批量评估等示例
  - 详细的使用说明和注意事项
  - 命令行参数示例
- **使用**: `python example_usage.py`

### 文档文件

#### 7. `MODEL_EVALUATOR_README.md` - 详细使用说明
- **内容**:
  - 完整的功能介绍和使用指南
  - 支持的模型和数据集列表
  - 评估指标的详细说明
  - 故障排除和性能优化建议

#### 8. `EVALUATOR_FILES_SUMMARY.md` - 文件说明（本文件）
- **内容**: 所有创建文件的详细说明和使用方法

## 支持的模型

### DeepLabV3+系列
- `deeplabv3plus_resnet50`
- `deeplabv3plus_resnet101`
- `deeplabv3plus_mobilenet`
- `deeplabv3plus_xception`

### SegFormer系列
- `segformer_b0` - `segformer_b5`

### Mask2Former系列
- `mask2former_swin_tiny`
- `mask2former_swin_small`
- `mask2former_swin_tiny_ade`
- `mask2former_swin_small_ade`

### UPerNet系列
- `upernet_swin_tiny`
- `upernet_swin_small`
- `upernet_swin_base`

### U-Net系列
- `unet_resnet50`
- `unet_resnet101`

## 支持的数据集

- **AI4Mars**: 5类火星地形分割 (Soil, Bedrock, Sand, Big Rock, Background)
- **AI4Mars-SMI**: 半监督扩展版本，5类
- **LabelMars6**: 6类火星地形分割

## 评估指标

### 1. FPS (Frames Per Second)
- **测试方法**: GPU预热 + 100次推理取平均
- **评估标准**: 高速(>100) / 中速(30-100) / 低速(<30)

### 2. FLOPs (Floating Point Operations)
- **测试方法**: 专业计算器统计所有层的浮点运算
- **评估标准**: 低(<5G) / 中(5G-50G) / 高(>50G)

### 3. Params (Parameters)
- **测试方法**: 统计所有可训练参数数量
- **评估标准**: 轻量(<1M) / 中等(1M-10M) / 大型(>10M)

## 使用流程

### 方法1: GUI界面（推荐新手）
```bash
python run_evaluator.py
```
1. 选择模型和参数
2. 点击开始评估
3. 查看详细报告

### 方法2: 批量评估（推荐研究）
```bash
# 评估所有模型
python batch_evaluator.py

# 评估指定模型
python batch_evaluator.py --models deeplabv3plus_resnet50 segformer_b0

# 自定义参数
python batch_evaluator.py --input-size 256 --batch-size 8 --output-dir results
```

### 方法3: 编程接口（推荐开发）
```python
from batch_evaluator import BatchEvaluator

evaluator = BatchEvaluator()
result = evaluator.evaluate_model("deeplabv3plus_resnet50", 512, 4, 5)
print(f"FPS: {result['fps']:.1f}")
```

## 输出文件

### GUI评估
- 界面内显示详细报告
- 包含硬件信息、配置参数、评估结果、效率分析

### 批量评估
- `evaluation_results_YYYYMMDD_HHMMSS.json`: 详细数据
- `evaluation_results_YYYYMMDD_HHMMSS.csv`: 表格数据
- `comparison_report_YYYYMMDD_HHMMSS.md`: 对比报告

## 环境要求

### 必需依赖
```bash
pip install torch torchvision numpy psutil pillow scikit-learn
```

### GUI依赖
```bash
# Linux
sudo apt-get install python3-tk

# Windows/macOS
# 通常已包含在Python安装中
```

### 项目结构
确保以下文件存在：
- `network/__init__.py`
- `network/modeling.py`
- `datasets/__init__.py`
- `utils/__init__.py`
- `backboned_unet/__init__.py`

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'network'
   ```
   **解决**: 在项目根目录运行

2. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   **解决**: 减小批次大小或输入尺寸

3. **GUI无法启动**
   ```
   ImportError: No module named '_tkinter'
   ```
   **解决**: 安装tkinter依赖

### 测试环境
```bash
python test_evaluator.py
```

## 扩展功能

### 添加新模型
1. 在`network/modeling.py`中实现模型函数
2. 确保函数签名: `model_name(num_classes, output_stride, pretrained_backbone)`
3. 重启评估器

### 添加新指标
1. 在`BatchEvaluator`类中添加计算方法
2. 更新报告生成函数
3. 修改GUI显示逻辑

### 自定义评估
```python
from flops_calculator import calculate_model_flops
import torch.nn as nn

# 自定义模型
model = nn.Sequential(...)

# 计算FLOPs
result = calculate_model_flops(model, (1, 3, 512, 512))
print(f"FLOPs: {result['total_flops_G']:.2f}G")
```

## 性能优化建议

1. **GPU使用**: 确保CUDA可用以获得准确测试
2. **内存管理**: 大模型使用小批次大小
3. **多次测试**: 关键模型建议多次运行取平均
4. **预热处理**: FPS测试包含GPU预热阶段

## 技术特点

- **专业FLOPs计算**: 支持多种深度学习层的精确计算
- **GPU同步测试**: 确保FPS测试的准确性
- **硬件信息检测**: 自动获取详细的硬件环境信息
- **多格式输出**: 支持JSON、CSV、Markdown等格式
- **友好的用户界面**: GUI和命令行两种使用方式
- **完整的错误处理**: 详细的错误提示和解决方案

这套工具为您的火星地形分割项目提供了完整的模型效率评估解决方案，可以帮助您选择最适合的模型架构。
