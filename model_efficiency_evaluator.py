#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Model Efficiency Evaluator GUI
用于评估火星地形分割模型效率的图形界面工具

Author: AI Assistant
Date: 2024
"""

import sys
import os
import time
import platform
import psutil
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from collections import OrderedDict
import numpy as np

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import network
    from datasets import AI4Mars, AI4MarsSMI, LabelMars6
    from utils import ext_transforms as et
    from backboned_unet import Unet
    from flops_calculator import FLOPsCalculator
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录下运行此脚本")

class ModelEfficiencyEvaluator:
    def __init__(self, root):
        self.root = root
        self.root.title("火星地形分割模型效率评估器")
        self.root.geometry("800x700")
        
        # 初始化变量
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.evaluation_running = False
        
        # 创建GUI界面
        self.create_widgets()
        
        # 获取可用模型列表
        self.get_available_models()
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="模型效率评估器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 模型选择
        ttk.Label(main_frame, text="选择模型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(main_frame, textvariable=self.model_var, 
                                       state="readonly", width=40)
        self.model_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 数据集选择
        ttk.Label(main_frame, text="选择数据集:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.dataset_var = tk.StringVar(value="AI4Mars")
        dataset_combo = ttk.Combobox(main_frame, textvariable=self.dataset_var,
                                    values=["AI4Mars", "AI4Mars-SMI", "LabelMars6"],
                                    state="readonly", width=40)
        dataset_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 输入尺寸选择
        ttk.Label(main_frame, text="输入尺寸:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.input_size_var = tk.StringVar(value="512")
        input_size_combo = ttk.Combobox(main_frame, textvariable=self.input_size_var,
                                       values=["256", "512", "768", "1024"],
                                       state="readonly", width=40)
        input_size_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 批次大小选择
        ttk.Label(main_frame, text="批次大小:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.batch_size_var = tk.StringVar(value="4")
        batch_size_combo = ttk.Combobox(main_frame, textvariable=self.batch_size_var,
                                       values=["1", "2", "4", "8", "16"],
                                       state="readonly", width=40)
        batch_size_combo.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 评估按钮
        self.eval_button = ttk.Button(main_frame, text="开始评估", 
                                     command=self.start_evaluation)
        self.eval_button.grid(row=5, column=0, columnspan=2, pady=20)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=7, column=0, columnspan=2, pady=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="评估结果", padding="10")
        result_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                         pady=10)
        result_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(8, weight=1)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, height=15, width=70)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def get_available_models(self):
        """获取可用的模型列表"""
        try:
            # 获取network.modeling中的所有模型
            available_models = []
            for name in dir(network.modeling):
                if (name.islower() and 
                    not name.startswith("_") and 
                    callable(getattr(network.modeling, name))):
                    available_models.append(name)
            
            # 添加U-Net模型
            available_models.extend(["unet_resnet50", "unet_resnet101"])
            
            self.model_combo['values'] = sorted(available_models)
            if available_models:
                self.model_var.set(available_models[0])
                
        except Exception as e:
            messagebox.showerror("错误", f"获取模型列表失败: {str(e)}")
            
    def get_num_classes(self, dataset_name):
        """根据数据集名称获取类别数"""
        dataset_classes = {
            "AI4Mars": 5,
            "AI4Mars-SMI": 5,
            "LabelMars6": 6
        }
        return dataset_classes.get(dataset_name, 5)
        
    def create_model(self, model_name, num_classes):
        """创建模型实例"""
        try:
            if model_name.startswith("unet_"):
                # U-Net模型
                backbone_name = model_name.split("_")[1]  # resnet50 或 resnet101
                model = Unet(backbone_name=backbone_name, classes=num_classes)
            else:
                # 其他模型
                model_func = getattr(network.modeling, model_name)
                model = model_func(num_classes=num_classes, output_stride=16)
            
            return model
        except Exception as e:
            raise Exception(f"创建模型失败: {str(e)}")
            
    def count_parameters(self, model):
        """计算模型参数数量"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
        
    def calculate_flops(self, model, input_size, batch_size):
        """计算FLOPs (使用专业计算器)"""
        try:
            calculator = FLOPsCalculator()
            input_shape = (batch_size, 3, input_size, input_size)
            result = calculator.calculate_flops(model, input_shape, str(self.device))
            return result['total_flops']

        except Exception as e:
            print(f"FLOPs计算错误: {e}")
            # 回退到简化计算方法
            return self._calculate_flops_fallback(model, input_size, batch_size)

    def _calculate_flops_fallback(self, model, input_size, batch_size):
        """FLOPs计算的回退方法"""
        try:
            dummy_input = torch.randn(batch_size, 3, input_size, input_size).to(self.device)

            # 计算前向传播的乘加操作数
            total_flops = 0

            def flop_count_hook(module, input, output):
                nonlocal total_flops
                if isinstance(module, nn.Conv2d):
                    # 卷积层FLOPs计算
                    kernel_flops = module.kernel_size[0] * module.kernel_size[1] * module.in_channels
                    output_elements = output.numel()
                    total_flops += kernel_flops * output_elements
                elif isinstance(module, nn.Linear):
                    # 全连接层FLOPs计算
                    total_flops += module.in_features * module.out_features * batch_size

            # 注册钩子
            hooks = []
            for module in model.modules():
                if isinstance(module, (nn.Conv2d, nn.Linear)):
                    hooks.append(module.register_forward_hook(flop_count_hook))

            # 前向传播
            model.eval()
            with torch.no_grad():
                _ = model(dummy_input)

            # 移除钩子
            for hook in hooks:
                hook.remove()

            return total_flops

        except Exception as e:
            print(f"回退FLOPs计算也失败: {e}")
            return 0

    def measure_fps(self, model, input_size, batch_size, num_iterations=100):
        """测量模型推理速度(FPS)"""
        try:
            model.eval()
            dummy_input = torch.randn(batch_size, 3, input_size, input_size).to(self.device)

            # 预热GPU
            with torch.no_grad():
                for _ in range(10):
                    _ = model(dummy_input)

            # 同步GPU
            if self.device.type == 'cuda':
                torch.cuda.synchronize()

            # 测量推理时间
            start_time = time.time()
            with torch.no_grad():
                for i in range(num_iterations):
                    _ = model(dummy_input)
                    # 更新进度
                    if i % 10 == 0:
                        progress = 50 + (i / num_iterations) * 40  # 50-90%的进度
                        self.update_progress(progress, f"FPS测试中... {i}/{num_iterations}")

            # 同步GPU
            if self.device.type == 'cuda':
                torch.cuda.synchronize()

            end_time = time.time()

            total_time = end_time - start_time
            fps = (num_iterations * batch_size) / total_time

            return fps

        except Exception as e:
            print(f"FPS测试错误: {e}")
            return 0

    def get_hardware_info(self):
        """获取硬件环境信息"""
        info = []

        # 系统信息
        info.append(f"操作系统: {platform.system()} {platform.release()}")
        info.append(f"处理器: {platform.processor()}")

        # 内存信息
        memory = psutil.virtual_memory()
        info.append(f"总内存: {memory.total / (1024**3):.1f} GB")
        info.append(f"可用内存: {memory.available / (1024**3):.1f} GB")

        # GPU信息
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            info.append(f"GPU: {gpu_name}")
            info.append(f"GPU内存: {gpu_memory:.1f} GB")
        else:
            info.append("GPU: 未检测到CUDA设备")

        # PyTorch版本
        info.append(f"PyTorch版本: {torch.__version__}")

        return info

    def update_progress(self, value, status):
        """更新进度条和状态"""
        self.progress_var.set(value)
        self.status_var.set(status)
        self.root.update_idletasks()

    def start_evaluation(self):
        """开始评估（在新线程中运行）"""
        if self.evaluation_running:
            return

        # 检查参数
        if not self.model_var.get():
            messagebox.showerror("错误", "请选择一个模型")
            return

        self.evaluation_running = True
        self.eval_button.config(state="disabled")
        self.result_text.delete(1.0, tk.END)

        # 在新线程中运行评估
        thread = threading.Thread(target=self.run_evaluation)
        thread.daemon = True
        thread.start()

    def run_evaluation(self):
        """运行模型评估"""
        try:
            # 获取参数
            model_name = self.model_var.get()
            dataset_name = self.dataset_var.get()
            input_size = int(self.input_size_var.get())
            batch_size = int(self.batch_size_var.get())
            num_classes = self.get_num_classes(dataset_name)

            self.update_progress(10, "正在创建模型...")

            # 创建模型
            model = self.create_model(model_name, num_classes)
            model = model.to(self.device)

            self.update_progress(20, "正在计算参数数量...")

            # 计算参数数量
            params = self.count_parameters(model)

            self.update_progress(30, "正在计算FLOPs...")

            # 计算FLOPs
            flops = self.calculate_flops(model, input_size, batch_size)

            self.update_progress(50, "正在测试FPS...")

            # 测量FPS
            fps = self.measure_fps(model, input_size, batch_size)

            self.update_progress(90, "正在生成报告...")

            # 获取硬件信息
            hardware_info = self.get_hardware_info()

            # 生成结果报告
            self.generate_report(model_name, dataset_name, input_size, batch_size,
                               params, flops, fps, hardware_info)

            self.update_progress(100, "评估完成")

        except Exception as e:
            messagebox.showerror("错误", f"评估过程中出现错误: {str(e)}")
            self.update_progress(0, "评估失败")
        finally:
            self.evaluation_running = False
            self.eval_button.config(state="normal")

    def generate_report(self, model_name, dataset_name, input_size, batch_size,
                       params, flops, fps, hardware_info):
        """生成评估报告"""
        report = []

        # 标题
        report.append("=" * 60)
        report.append("火星地形分割模型效率评估报告")
        report.append("=" * 60)
        report.append("")

        # 评估配置
        report.append("评估配置:")
        report.append(f"  模型: {model_name}")
        report.append(f"  数据集: {dataset_name}")
        report.append(f"  输入尺寸: {input_size}×{input_size}")
        report.append(f"  批次大小: {batch_size}")
        report.append(f"  类别数量: {self.get_num_classes(dataset_name)}")
        report.append("")

        # 硬件环境
        report.append("硬件环境:")
        for info in hardware_info:
            report.append(f"  {info}")
        report.append("")

        # 评估结果
        report.append("评估结果:")
        report.append(f"  参数数量 (Params): {params / 1e6:.2f} M")
        report.append(f"  浮点运算数 (FLOPs): {flops / 1e9:.2f} G")
        report.append(f"  推理速度 (FPS): {fps:.1f}")
        report.append("")

        # 效率分析
        report.append("效率分析:")
        if params < 1e6:
            report.append("  ✓ 轻量级模型 (参数 < 1M)")
        elif params < 10e6:
            report.append("  ○ 中等规模模型 (参数 1M-10M)")
        else:
            report.append("  ✗ 大型模型 (参数 > 10M)")

        if flops < 5e9:
            report.append("  ✓ 低计算复杂度 (FLOPs < 5G)")
        elif flops < 50e9:
            report.append("  ○ 中等计算复杂度 (FLOPs 5G-50G)")
        else:
            report.append("  ✗ 高计算复杂度 (FLOPs > 50G)")

        if fps > 100:
            report.append("  ✓ 高推理速度 (FPS > 100)")
        elif fps > 30:
            report.append("  ○ 中等推理速度 (FPS 30-100)")
        else:
            report.append("  ✗ 低推理速度 (FPS < 30)")

        report.append("")
        report.append(f"评估时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)

        # 显示报告
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "\n".join(report))

def main():
    """主函数"""
    root = tk.Tk()
    app = ModelEfficiencyEvaluator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
