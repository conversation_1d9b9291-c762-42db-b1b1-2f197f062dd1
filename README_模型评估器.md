# 火星地形分割模型效率评估器

## 概述

这是一个专门用于评估火星地形语义分割模型效率的完整工具，集成了GUI界面、批量评估、专业FLOPs计算等功能。

## 功能特点

### 🎯 评估指标
- **FPS (推理速度)**: GPU预热 + 100次推理取平均值
- **FLOPs (计算复杂度)**: 专业计算器统计所有层的浮点运算数
- **Params (模型参数)**: 统计所有可训练参数数量

### 🚀 支持的模型
- **DeepLabV3+系列**: resnet50/101, mobilenet, xception等
- **SegFormer系列**: b0-b5全系列
- **Mask2Former系列**: swin_tiny/small等
- **UPerNet系列**: swin_tiny/small/base
- **U-Net系列**: resnet50/101

### 📊 支持的数据集
- **AI4Mars**: 5类火星地形 (Soil, Bedrock, Sand, Big Rock, Background)
- **AI4Mars-SMI**: 半监督扩展版本
- **LabelMars6**: 6类火星地形

## 安装要求

### 必需依赖
```bash
pip install torch torchvision numpy psutil
```

### GUI依赖 (可选)
```bash
# Linux
sudo apt-get install python3-tk

# Windows/macOS通常已包含
```

## 使用方法

### 1. GUI界面模式 (推荐)
```bash
python model_evaluator_all_in_one.py
```
- 选择模型、数据集、输入尺寸、批次大小
- 点击"开始评估"
- 查看详细报告

### 2. 批量评估模式
```bash
# 评估所有模型
python model_evaluator_all_in_one.py --batch

# 评估指定模型
python model_evaluator_all_in_one.py --batch --models deeplabv3plus_resnet50 segformer_b0

# 自定义参数
python model_evaluator_all_in_one.py --batch --input-size 256 --batch-size 8 --output-dir results
```

### 3. 测试环境
```bash
python model_evaluator_all_in_one.py --test
```

## 输出结果

### GUI模式
- 界面内显示完整评估报告
- 包含硬件信息、配置参数、评估结果、效率分析

### 批量模式
- `evaluation_results_YYYYMMDD_HHMMSS.json`: 详细数据
- `evaluation_results_YYYYMMDD_HHMMSS.csv`: 表格数据  
- `comparison_report_YYYYMMDD_HHMMSS.md`: 对比报告

## 评估标准

### FPS (推理速度)
- ✅ 高速: > 100 FPS
- ⚠️ 中速: 30-100 FPS
- ❌ 低速: < 30 FPS

### FLOPs (计算复杂度)
- ✅ 低: < 5G FLOPs
- ⚠️ 中: 5G-50G FLOPs
- ❌ 高: > 50G FLOPs

### Params (模型大小)
- ✅ 轻量: < 1M 参数
- ⚠️ 中等: 1M-10M 参数
- ❌ 大型: > 10M 参数

## 示例报告

```
============================================================
火星地形分割模型效率评估报告
============================================================

评估配置:
  模型: deeplabv3plus_resnet50
  数据集: AI4Mars
  输入尺寸: 512×512
  批次大小: 4
  类别数量: 5

硬件环境:
  操作系统: Windows 10
  GPU: NVIDIA RTX 4090
  GPU内存: 24.0 GB
  PyTorch版本: 2.0.0

评估结果:
  参数数量 (Params): 39.64 M
  浮点运算数 (FLOPs): 182.66 G
  推理速度 (FPS): 152.5

效率分析:
  ✗ 大型模型 (参数 > 10M)
  ✗ 高计算复杂度 (FLOPs > 50G)
  ✅ 高推理速度 (FPS > 100)
============================================================
```

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'network'
   ```
   **解决**: 在项目根目录运行

2. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   **解决**: 减小批次大小或输入尺寸

3. **GUI无法启动**
   ```
   ImportError: No module named '_tkinter'
   ```
   **解决**: 使用 `--batch` 模式或安装tkinter

### 性能优化

1. **GPU使用**: 确保CUDA可用以获得准确测试
2. **内存管理**: 大模型使用小批次大小
3. **多次测试**: 关键模型建议多次运行

## 技术实现

### FLOPs计算原理
- 支持Conv2d, ConvTranspose2d, BatchNorm2d, ReLU, Pooling, Linear等层
- 使用前向钩子函数精确统计每层运算数
- 自动处理不同层类型的计算复杂度

### FPS测试原理
- GPU预热: 10次前向传播
- 同步测试: `torch.cuda.synchronize()`确保准确计时
- 批量测试: 100次推理取平均值

### 硬件检测
- 自动获取CPU、内存、GPU信息
- 支持CUDA和CPU环境
- 详细的系统环境报告

## 扩展功能

### 添加新模型
1. 在`network/modeling.py`中实现模型函数
2. 确保函数签名符合标准
3. 重启评估器即可使用

### 编程接口
```python
from model_evaluator_all_in_one import ModelEvaluator

evaluator = ModelEvaluator()
result = evaluator.evaluate_model("deeplabv3plus_resnet50", 512, 4, 5)
print(f"FPS: {result['fps']:.1f}")
```

## 命令行参数

```
python model_evaluator_all_in_one.py [选项]

选项:
  --batch              批量评估模式
  --test               运行测试
  --models MODEL [MODEL ...]  指定要评估的模型
  --input-size SIZE    输入图像尺寸 (默认: 512)
  --batch-size SIZE    批次大小 (默认: 4)
  --num-classes NUM    类别数量 (默认: 5)
  --output-dir DIR     输出目录 (默认: evaluation_results)
  --help               显示帮助信息
```

## 许可证

本工具基于项目原有许可证，仅用于学术研究和教育目的。

---

**注意**: 确保在项目根目录下运行此工具，以便正确导入项目模块。
