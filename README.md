# Mars Rover Terrain Semantic Segmentation Project

## Project Background and Motivation

This project implements deep learning-based semantic segmentation models for Mars rover terrain classification. Accurate terrain classification is crucial for autonomous navigation and path planning of Mars rovers, enabling them to avoid hazardous areas (such as loose sand and large rocks) and select safe paths (such as solid soil and bedrock).

Mars rovers operate in challenging environments where communication delays with Earth can range from 5 to 20 minutes. This delay makes real-time remote control impossible, necessitating autonomous navigation capabilities. Terrain classification is a critical component of this autonomy, as different terrain types present varying levels of risk:

- **Soil**: Generally safe for traversal
- **Bedrock**: Very stable and safe for traversal
- **Sand**: Potentially hazardous due to wheel slippage and sinkage
- **Big Rocks**: Obstacles that must be avoided

This project evaluates two state-of-the-art semantic segmentation models (U-Net and DeepLabV3+) on this critical task. The models are tested on two primary datasets: AI4Mars and LabelMars. Additionally, we explore data augmentation using generative adversarial networks (semanticStyleGAN) to improve recognition of rare terrain classes.

## Datasets

### AI4Mars
AI4Mars is a terrain annotation dataset released by NASA's Jet Propulsion Laboratory (JPL), containing images from the Curiosity Mars rover. The dataset includes the following classes:

- **Soil**: Relatively firm terrain with small, loose particles
- **Bedrock**: Solid rock outcrops
- **Sand**: Loose, fine-grained material that poses mobility risks
- **Big Rock**: Large, individual rocks that are obstacles
- **Background/Rover**: Parts of the rover visible in images or sky

The dataset is divided into training, validation, and test sets, with additional Mars-specific test splits (M1, M2, M3) representing different Martian terrain regions.

### AI4Mars-SMI
AI4Mars-SMI is an extended version of AI4Mars created through semi-supervised learning methods. Compared to the original AI4Mars, it contains approximately twice the number of training and validation images, while maintaining the same test set for fair comparison. The key advantages of AI4Mars-SMI include:

1. **Increased data volume**: ~2x more training and validation images
2. **Consistent format**: Maintains the same data format and class definitions as AI4Mars
3. **Improved rare class representation**: Better representation of underrepresented classes like sand and big rocks
4. **Compatible data loading**: Can use the same data loading and processing code as AI4Mars

The semi-supervised approach used to create AI4Mars-SMI involved:
- Training initial models on the labeled AI4Mars dataset
- Using these models to generate pseudo-labels for unlabeled Mars rover images
- Filtering and refining these pseudo-labels based on confidence thresholds
- Incorporating the high-confidence pseudo-labeled images into the training set

### LabelMars
LabelMars is another Mars terrain annotation dataset used for cross-validation of model performance. It provides an independent test set to verify the generalization capabilities of the trained models.

## Model Architectures

This project implements two primary semantic segmentation architectures:

### U-Net

U-Net features an encoder-decoder architecture with skip connections that help preserve spatial information:

- **Encoder**: Progressively reduces spatial dimensions while increasing feature channels
  - Supports multiple ResNet backbones (ResNet18, ResNet34, ResNet50, ResNet101)
  - Each encoder block includes convolution, batch normalization, and ReLU activation
  - Max pooling reduces spatial dimensions between blocks

- **Decoder**: Progressively increases spatial dimensions while decreasing feature channels
  - Each decoder block includes transposed convolution for upsampling
  - Skip connections from encoder concatenate features at corresponding resolutions
  - Each block includes convolution, batch normalization, and ReLU activation

- **Final Layer**: 1x1 convolution that maps feature channels to the number of classes

- **Skip Connections**: Direct connections between encoder and decoder at matching resolutions
  - Preserve fine-grained spatial details lost during downsampling
  - Enable precise localization of terrain boundaries

U-Net is particularly effective for tasks requiring fine boundary delineation, making it suitable for distinguishing between adjacent terrain types.

### DeepLabV3+

DeepLabV3+ combines atrous (dilated) convolutions with spatial pyramid pooling:

- **Encoder**: Modified ResNet backbone with atrous convolutions
  - Supports multiple ResNet variants (ResNet18, ResNet34, ResNet50, ResNet101)
  - Atrous convolutions increase receptive field without reducing resolution
  - Output stride options: 8 (higher resolution, more memory) or 16 (lower resolution, less memory)

- **Atrous Spatial Pyramid Pooling (ASPP)**:
  - Parallel atrous convolutions with different dilation rates (6, 12, 18)
  - Global average pooling branch for global context
  - 1x1 convolution for dimension reduction
  - Concatenation of all branches followed by 1x1 convolution

- **Decoder**:
  - Bilinear upsampling of ASPP features
  - Skip connection from low-level features in the backbone
  - 3x3 convolutions for feature refinement
  - Final upsampling to input resolution

DeepLabV3+ excels at capturing multi-scale context while maintaining high-resolution feature maps, making it effective for terrain classification where both local texture and broader context matter.

## Technical Details and Design Decisions

### Background Class Exclusion
- Background/rover class is excluded from training and testing
- Rationale: This class represents parts of the rover itself or sky, which are not relevant for terrain classification
- Implementation: Masked out during loss calculation and evaluation metrics

### Early Stopping Strategy
- Monitors validation loss or IoU with patience parameter (default: 10 epochs)
- Saves best model checkpoint based on validation performance
- Reduces training time while maintaining high accuracy
- Implementation: Custom EarlyStopping class that tracks best performance and triggers stopping when no improvement occurs

### Input Image Size
- Larger input sizes (512x512) improve segmentation performance
- Rationale: Higher resolution preserves fine-grained terrain details
- Trade-off: Increased memory requirements and computation time
- Implementation: Images are resized and randomly cropped during training

### Backbone Selection
- ResNet50: Preferred for deployment due to efficiency
  - Good balance between accuracy and computational requirements
  - Suitable for potential onboard deployment with limited resources
- ResNet101: Provides slightly better accuracy (+1-2% IoU)
  - Higher computational cost
  - Better for research and offline analysis

### GAN Data Augmentation
- Uses semanticStyleGAN to generate synthetic training images
- Particularly beneficial for rare classes (sand, big rocks)
- Improves rare class IoU by 1-2%
- Implementation: Generated images are added to the training set with appropriate labels

### Optimizer and Learning Rate
- SGD optimizer with momentum (0.9) and weight decay (1e-4)
- Initial learning rate: 0.01
- Polynomial learning rate decay: lr = initial_lr * (1 - iter/max_iter)^power
- Power parameter: 0.9
- Rationale: Gradual learning rate reduction helps fine-tune model weights

### Data Augmentation
- Resizing: Maintains aspect ratio while fitting target dimensions
- Random cropping: Creates 512x512 patches during training
- Horizontal flipping: 50% probability
- Normalization: Mean=[0.485, 0.456, 0.406], Std=[0.229, 0.224, 0.225]
- Implementation: PyTorch transforms applied during dataset loading

## Training Strategy

### Loss Functions
The project supports multiple loss functions:

- **Cross-Entropy Loss (default)**:
  - Standard pixel-wise classification loss
  - Class weights can be applied to address class imbalance
  - Implementation: `nn.CrossEntropyLoss` with optional `weight` parameter

- **Focal Loss**:
  - Addresses class imbalance by down-weighting well-classified examples
  - Gamma parameter (default: 2) controls the down-weighting strength
  - Particularly useful for datasets with significant class imbalance
  - Implementation: Custom `FocalLoss` class

- **Dice Loss**:
  - Based on Dice coefficient (F1 score)
  - Less sensitive to class imbalance than cross-entropy
  - Implementation: Custom `DiceLoss` class

### Learning Rate Strategy
- Polynomial decay learning rate schedule
- Formula: lr = initial_lr * (1 - iter/max_iter)^power
- Initial learning rate: 0.01
- Power parameter: 0.9
- Implementation: Custom learning rate scheduler updated each iteration

### Batch Size
- Default: 16
- Adjustable based on GPU memory
- Implementation: Command-line parameter `--batch_size`

### Validation Strategy
- Regular evaluation on validation set (default: every 1 epoch)
- Metrics calculated: IoU (per-class and mean), pixel accuracy
- Optional visualization of predictions
- Implementation: `validate` function with configurable interval

### Early Stopping Mechanism
- Monitors validation loss or IoU
- Patience parameter (default: 10 epochs)
- Saves best model checkpoint
- Implementation: Custom `EarlyStopping` class

## Evaluation Metrics

### IoU (Intersection over Union)
- Primary evaluation metric
- Calculated per class and averaged
- Formula: IoU = TP / (TP + FP + FN)
  - TP: True Positives
  - FP: False Positives
  - FN: False Negatives
- Implementation: Custom metric calculation in `utils/metrics.py`

### Pixel Accuracy
- Overall pixel classification accuracy
- Formula: Accuracy = (TP + TN) / (TP + TN + FP + FN)
- Implementation: Calculated during validation

### F1 Score
- Harmonic mean of precision and recall
- Formula: F1 = 2 * (Precision * Recall) / (Precision + Recall)
- Implementation: Derived from confusion matrix during evaluation

## Usage Guide

### Environment Requirements
- Python 3.6+
- PyTorch 1.0+
- CUDA-capable GPU (for training)
- Other dependencies:
  - tqdm: Progress bars
  - numpy: Numerical operations
  - PIL: Image processing
  - matplotlib: Visualization
  - sklearn: Metrics calculation
  - visdom: Training visualization (optional)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd Unet_DeepLabV3Plus_Pytorch

# Install dependencies
pip install -r requirements.txt

# Optional: Install Visdom for visualization
pip install visdom