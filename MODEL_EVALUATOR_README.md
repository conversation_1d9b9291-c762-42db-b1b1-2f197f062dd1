# 火星地形分割模型效率评估器

## 概述

这是一个专门用于评估火星地形语义分割模型效率的GUI工具。该工具可以测量模型的三个关键指标：
- **FPS (Frames Per Second)**: 推理速度
- **FLOPs (Floating Point Operations)**: 浮点运算数
- **Params (Parameters)**: 模型参数数量

## 功能特点

### 🎯 支持的模型
- **DeepLabV3+系列**: deeplabv3plus_resnet50, deeplabv3plus_resnet101, deeplabv3plus_mobilenet等
- **SegFormer系列**: segformer_b0, segformer_b1, segformer_b2, segformer_b3, segformer_b4, segformer_b5
- **Mask2Former系列**: mask2former_swin_tiny, mask2former_swin_small等
- **UPerNet系列**: upernet_swin_tiny, upernet_swin_small, upernet_swin_base
- **U-Net系列**: unet_resnet50, unet_resnet101

### 📊 支持的数据集
- **AI4Mars**: NASA JPL发布的火星地形数据集 (5类)
- **AI4Mars-SMI**: 半监督学习扩展的AI4Mars数据集 (5类)
- **LabelMars6**: 独立的火星地形数据集 (6类)

### ⚙️ 可配置参数
- **输入尺寸**: 256×256, 512×512, 768×768, 1024×1024
- **批次大小**: 1, 2, 4, 8, 16
- **硬件环境**: 自动检测CPU/GPU信息

## 安装要求

### 系统要求
- Python 3.7+
- CUDA支持的GPU (推荐，CPU也可运行但速度较慢)

### 依赖包
```bash
pip install torch torchvision numpy psutil pillow scikit-learn
```

### GUI依赖 (Linux)
```bash
sudo apt-get install python3-tk
```

### GUI依赖 (Windows/macOS)
通常Python安装时已包含tkinter

## 使用方法

### 1. 快速启动
```bash
# 在项目根目录下运行
python run_evaluator.py
```

### 2. 直接启动GUI
```bash
python model_efficiency_evaluator.py
```

### 3. GUI操作步骤

1. **选择模型**: 从下拉菜单中选择要评估的模型
2. **选择数据集**: 选择对应的数据集类型
3. **设置输入尺寸**: 根据需要选择输入图像尺寸
4. **设置批次大小**: 选择合适的批次大小
5. **开始评估**: 点击"开始评估"按钮
6. **查看结果**: 在结果区域查看详细的评估报告

## 评估指标说明

### FPS (推理速度)
- **测试方法**: 在指定硬件上运行100次推理，计算平均每秒处理帧数
- **评估标准**:
  - ✅ 高速: FPS > 100
  - ⚠️ 中速: 30 < FPS ≤ 100  
  - ❌ 低速: FPS ≤ 30

### FLOPs (计算复杂度)
- **测试方法**: 使用专业的FLOPs计算器统计前向传播的浮点运算数
- **评估标准**:
  - ✅ 低复杂度: FLOPs < 5G
  - ⚠️ 中复杂度: 5G ≤ FLOPs < 50G
  - ❌ 高复杂度: FLOPs ≥ 50G

### Params (模型大小)
- **测试方法**: 统计模型中所有可训练参数的数量
- **评估标准**:
  - ✅ 轻量级: Params < 1M
  - ⚠️ 中等规模: 1M ≤ Params < 10M
  - ❌ 大型模型: Params ≥ 10M

## 输出报告示例

```
============================================================
火星地形分割模型效率评估报告
============================================================

评估配置:
  模型: deeplabv3plus_resnet101
  数据集: AI4Mars
  输入尺寸: 512×512
  批次大小: 4
  类别数量: 5

硬件环境:
  操作系统: Linux 5.4.0
  处理器: Intel(R) Core(TM) i7-9700K
  总内存: 32.0 GB
  可用内存: 28.5 GB
  GPU: NVIDIA GeForce RTX 3080
  GPU内存: 10.0 GB
  PyTorch版本: 1.12.0

评估结果:
  参数数量 (Params): 59.34 M
  浮点运算数 (FLOPs): 182.66 G
  推理速度 (FPS): 45.2

效率分析:
  ✗ 大型模型 (参数 > 10M)
  ✗ 高计算复杂度 (FLOPs > 50G)
  ○ 中等推理速度 (FPS 30-100)

评估时间: 2024-01-15 14:30:25
============================================================
```

## 技术实现

### FLOPs计算原理
- 使用自定义的`FLOPsCalculator`类
- 支持Conv2d, ConvTranspose2d, BatchNorm2d, ReLU, Pooling, Linear等层
- 通过前向钩子函数精确统计每层的浮点运算数

### FPS测试原理
- GPU预热: 运行10次前向传播
- 同步测试: 使用`torch.cuda.synchronize()`确保准确计时
- 批量测试: 运行100次推理取平均值

### 硬件信息获取
- 使用`psutil`获取系统和内存信息
- 使用`torch.cuda`获取GPU信息
- 自动适配CPU/GPU环境

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'network'
   ```
   **解决方案**: 确保在项目根目录下运行脚本

2. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   **解决方案**: 减小批次大小或输入尺寸

3. **GUI无法启动**
   ```
   ImportError: No module named '_tkinter'
   ```
   **解决方案**: 安装tkinter依赖包

### 性能优化建议

1. **GPU使用**: 确保CUDA可用以获得准确的GPU性能测试
2. **内存管理**: 大模型测试时建议使用较小的批次大小
3. **多次测试**: 对于关键模型，建议多次运行取平均值

## 扩展功能

### 添加新模型
1. 在`network/modeling.py`中实现模型函数
2. 确保函数签名符合标准格式
3. 重启评估器即可在下拉菜单中看到新模型

### 添加新数据集
1. 在`datasets/`目录下实现数据集类
2. 在`model_efficiency_evaluator.py`中添加数据集配置
3. 更新类别数量映射

## 许可证

本工具基于项目原有许可证发布，仅用于学术研究和教育目的。

## 联系方式

如有问题或建议，请通过项目Issues页面反馈。
