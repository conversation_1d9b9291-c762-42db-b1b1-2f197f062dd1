# LBNet: A Lightweight Bilateral Network for Semantic Segmentation of Martian Rock

PENGFEI WEI \( {}^{-1} \) , ZEZHOU SUN \( {}^{2} \) , AND HE TIAN \( {}^{2,3} \)

\( {}^{1} \) School of Mechanical and Aerospace Engineering, Jilin University, Changchun 130025, China

\( {}^{2} \) Beijing Institute of Spacecraft System Engineering, Beijing 100094, China

\( {}^{3} \) Beijing Key Laboratory of Intelligent Space Robotic System Technology and Applications, Beijing 100094, China

Corresponding author: <PERSON><PERSON><PERSON> (<EMAIL>)

*ABSTRACT Rock segmentation on the Martian surface is particularly critical for rover navigation, obstacle avoidance, and scientific target detection. We propose a lightweight bilateral network for semantic segmentation of Martian rock (LBNet). The network consists of a shallow spatial detail branch (SDB) and a deep semantic information branch (SIB). In the shallow spatial detail branch, dense connection channel aggregation convolution (CAConv) is adopted to establish local dependencies for each pixel and preserve detailed information. In the deep semantic information branch, channel split convolution (CSConv) is adopted to extract features by adopting different convolution kernels on different channel, reducing the similarity between different feature maps and increasing feature maps diversity. Finally, a feature fusion module (FFM) is designed to effectively fuse feature maps at different levels. With only 0.37M parameters, the model achieved \( {93.85}\% \mathrm{{mIoU}} \) and 147.8 FPS on the dataset of Perseverance, and \( {88.62}\% \mathrm{{mIoU}} \) and 152.5 FPS on the Curiosity dataset. Experiments show that the model achieves a good balance between segmentation accuracy and inference speed.

INDEX TERMS Semantic segmentation, rock segmentation, feature fusion, deep learning.

## I. INTRODUCTION

Mars is the planet in the solar system that is close to the Earth and its natural environment is similar to that of the Earth. It has always been the preferred target for human beings to go out of the Earth-Moon system to conduct deep space exploration [1]. Since the Soviet Union launched the Mars probe in 1960, countries around the world have carried out a total of 49 times Martian exploration activities [2]. The main goal of the Martian exploration mission is to explore the information about the activities of life on Mars, including the presence or absence of life on Mars in the past and at present, to understand the geography of Mars, including climate, geological conditions, and so on [3].

Rocks on the surface of Mars have a high distribution density, rich variety, varying shapes and sizes, and have obvious weathering and erosion characteristics. They have always been the main scientific goal of Mars exploration [4]. In addition, accurate rock identification can provide important data support for tasks such as path planning, obstacle avoidance and autonomous navigation of the Mars rover. Since the launch of the Mars exploration mission, computer vision-based rock detection technology on the surface of Mars has been around for decades [5].

Rock detection on the Martian surface could be divided into traditional visual detection methods and machine learning-based detection methods. Traditional visual detection methods detect rocks by extracting natural features (such as size, color, outline, edge, texture, etc.) or artificial features (such as gray histogram, moment feature, entropy, etc.) [6]. Traditional machine learning and deep learning are the two main branches of machine learning. Traditional machine learning mainly maps the feature information of the image to a higher dimension to identify the rocks in the image by the learning method, while deep learning is that the model itself refines the data without special transformation of the data [7].

Former rock detection methods on the Martian surface are mainly based on traditional methods. The OASIS system carried by the Spirit rover adopted the Sobel and Canny operator to detect the edge to identify the rocks [8]. The Rockster system on the Curiosity and Opportunity rovers adopted the canny operator to identify closed rock contours and got an accurate description of the rocks' shape [9]. Fox [10] combined image intensity information and height information to segment rocks from the background for subsequent rock classification. Song and Shan [11] proposed a rock segmentation method based on image texture and multi-resolution clustering. Gor [12] adopted strength information to search rocks with small sizes and distance information to search rocks with large sizes in the image, distance-based and intensity-based methods complement each other. Xiao [13] proposed a rock detection method based on homogeneous region-level strength information and spatial layout, which reduces the memory footprint by segmenting rocks at the region level. Dunlop [14] combined shape, texture, and shadow information to segment Martian rocks. Yang and Kang [15] proposed a method for Martian rock detection based on the combination of gradient-based information and local adaptive region information, which effectively alleviates the weak edge problem.

---

The associate editor coordinating the review of this manuscript and approving it for publication was Senthil Kumar \( {}^{12} \) .

---

In recent years, some scholars have begun to adopt machine learning to detect rocks on the Martian surface. Rashno [16] proposed a new feature vector of image pixels and adopted an ant colony optimization algorithm for feature selection, which provides the most relevant features to support vector machine classifiers for highly accurate pixel classification for rock detection. Wagstaff [17] employed a random forest classifier to detect rocks on Mars. Furlán [18] adopted the U-Net to segment rocks in a Mars-like environment. Furlán [19] also adopted the improved SSD network to detect rocks in the image, they substituted the Resnet50 for VGG16 as the backbone, which achieved better detection results. Li [20] adopted a U-shaped model that adopts a VGG16 network in the encoder, in the decoder, they adopted a multiscale dilated convolution and a deeper convolution layer, they achieved \( {81.02}\% \mathrm{{mIoU}} \) on the dataset of Mars collected by the Curiosity rover. Kuang [21] proposed a rock segmentation framework, called NI-U-Net++, they achieved 89.91% IoU on Cranfield Online Research Data. Swan [22] adopted ResNet-101 as a backbone for DeepLabv3+, on the dataset of AI4Mars, the model got over \( {96}\% \) overall classification accuracy. Lv [23] also proposed a modified U-shaped network that combines the ResNet101 in the encoder and U-Net in the decoder, on the dataset of TWMARS collected by the 'ZhuRong' rover they achieved \( {77.4}\% \) mIoU. Chiodini [24] adopted DeepLabv3+ to segment rocks and got comparable performances on the ESA Katwijk Beach Planetary Rover Dataset. Liu [25] propose a novel semantic segmentation network based on U-Net network and Transformer for deep space rock images, adopted global-local feature extraction (GLFE) and feature cross-fusion path (FCFP) to improving the network performance. Zhang [26] proposed a semi-supervised learning framework for semantic segmentation of Mars images. The framework takes into account the characteristics of Mars data and proposes two new enhancement methods which can effectively improve model performance. Xiong [27] and Liu [28] proposed an improved transformer model for Martian rock segmentation, these methods achieved good results.

For the detection of rocks on the Martian surface, traditional methods mainly rely on the manual design of the extractor, which requires a lot of expertise and a complex process of parameter adjustment. At the same time, each method aims at a specific application, the generalization ability and robustness are poor, and it is difficult to apply in complex Martian environments. Traditional machine learning methods adopt feature engineering to extract and clean the data artificially, the features of the model need to be manually coded, and the quality of features directly determines the performance of the model, so it is difficult to determine the best features. Deep learning belongs to representation learning, the model itself refines the data and does not need to select features, compress dimensions, and transform formats. Automatic feature extraction is the biggest advantage of deep learning. By learning from large number of samples, the model can acquire features that are specific to a particular dataset, and more efficiently and accurately expresses the data set. The abstract features extracted are also more robust and have a better generalization ability. It is more suitable for rock segmentation on the Martian surface.

Mars rovers have very limited operating time and can not carry large amounts of energy, in addition, the memory carried by the rover requires multiple programs to run simultaneously and its computational resources are very limited [29]. Existing deep learning-based image segmentation methods usually adopt large models, such as Unet [30], UNet++ [31], HRnet [32], RefineNet [33], DenseASPP [34], and transformer based network [35]. which have a large amount of computation and low running speed, it is difficult to apply to real Mars exploration. Therefore, the lighter the rock semantic segmentation model, the more suitable it is for deployment [36]. We propose a lightweight network for Martian surface rock segmentation, which greatly reduces the model size and increases inference speed while maintaining accuracy.

## II. METHOD

### A.THE OVERALL ARCHITECTURE

In image segmentation, dual-branch networks significantly improve segmentation accuracy and processing ability for complex scenes by extracting features of different scales or types [37]. The proposed lightweight bilateral network structure is shown in Fig. 1. It mainly consists of four parts: the conv stem module, parallel dual-branch network, feature fusion module, and seg head module. The conv stem module is a convolutional layer with stride 2 and a kernel size of \( 3 \times  3 \) , which is adopted to collect initial feature maps. In the conv stem module, we only adopt the downsampling operation once, which greatly preserves the spatial information. The dual branch network consists of a spatial detail branch and a semantic information branch. The spatial detail branch consists of two dense connection channel aggregation convolutions to obtain more detailed shallow information. The semantic information branch consists of a carefully designed channel split convolution and a feature fusion module to capture more comprehensive semantic information. The feature fusion module fuses feature maps at different levels in both spatial and channel dimensions to make full use of detailed feature maps at different levels. The seg head module restores the feature map to the original image size and predict the category of each pixel. The detailed architectural configuration of a lightweight bilateral network is shown in Table 1.

![bo_d1svja3ef24c73amk3pg_2_345_193_1020_673_0.jpg](images/bo_d1svja3ef24c73amk3pg_2_345_193_1020_673_0.jpg)

FIGURE 1. The architecture of the proposed model.

## B. CHANNEL AGGREGATION CONVOLUTION

![bo_d1svja3ef24c73amk3pg_2_164_1421_610_349_0.jpg](images/bo_d1svja3ef24c73amk3pg_2_164_1421_610_349_0.jpg)

FIGURE 2. Visualization of some feature maps generated by the first residual in ResNet-50. The upper left corner is the input image, and many feature maps show great similarity.

As shown in Fig. 2, there is a high similarity between the feature maps of different channels. To make full use of this similarity, Partial convolution only convolves 1/4 of the feature maps and concatenates the obtained feature maps with the remaining \( 3/4 \) of the feature maps to obtain new feature maps [38]. Ghostnet only performs depthwise convolution on \( 1/2 \) of the feature maps and concatenates the obtained feature maps with the remaining \( 1/2 \) of the feature maps to obtain new feature maps [39]. Although the above method can reduce the amount of calculation, it is difficult to determine whether the convolved feature maps contain necessary detail information or redundant feature maps. To this end, we propose a simple and direct method to reduce computational redundancy while convolving all feature maps, which we call channel aggregation convolution (CAConv). Fig. 3(a) illustrates how our CAConv works. CAConv first adopts the Channel Aggregation module to fuse the input feature maps. Then, a dilated convolution with a kernel size of \( 3 \times  3 \) is adopted to extract local information. Finally, pointwise convolution restores the number of channels to the same as the input feature maps and adds them to the input feature maps. The channel aggregation module is shown in Fig. 3(b). It only needs to split the feature maps equally into multiple groups on the channel dimension and add them to obtain the fused feature maps. We call the number of groups the aggregation rat. Partial convolution and Ghostnet only perform convolution operations on partial feature maps, and the other feature maps remain unchanged. In contrast, the proposed CAConv module convolve all features with less computation. We compare the performance of the three methods in the ablation study section.

TABLE 1. Detailed architectural configuration of lightweight bilateral network.

<table><tr><td colspan="4">Input Image(Size: \( 3 \times  {512} \times  {512} \) )</td></tr><tr><td colspan="4">Conv Stem Module (Size: \( {16} \times  {256} \times  {256} \) )</td></tr><tr><td colspan="2">SIB</td><td colspan="2">SDB</td></tr><tr><td>Layer</td><td>Channel</td><td>Layer</td><td>Size</td></tr><tr><td>CSConv</td><td>\( {16} \times  {256} \times  {256} \)</td><td rowspan="2">CAConv (d = 1, r = 2)</td><td rowspan="2">16×256×256</td></tr><tr><td>Downsample</td><td>\( {32} \times  {128} \times  {128} \)</td></tr><tr><td>CSConv</td><td>\( {32} \times  {128} \times  {128} \)</td><td rowspan="2">CAConv (d = 3, r = 2)</td><td rowspan="2">\( {16} \times  {256} \times  {256} \)</td></tr><tr><td>Downsample</td><td>\( {64} \times  {64} \times  {64} \)</td></tr><tr><td>CSConv</td><td>\( {64} \times  {64} \times  {64} \)</td><td rowspan="2">CAConv (d = 5, r = 2)</td><td rowspan="2">\( {16} \times  {256} \times  {256} \)</td></tr><tr><td>Downsample</td><td>128×32×32</td></tr><tr><td>CSConv</td><td>\( {128} \times  {32} \times  {32} \)</td><td rowspan="2">CAConv (d = 7, r = 2)</td><td rowspan="2">16×256×256</td></tr><tr><td>Upsample</td><td>\( {64} \times  {64} \times  {64} \)</td></tr><tr><td>FFM</td><td>\( {64} \times  {64} \times  {64} \)</td><td rowspan="2">CAConv (d = 1, r = 2)</td><td rowspan="2">16×256×256</td></tr><tr><td>CSConv</td><td>\( {64} \times  {64} \times  {64} \)</td></tr><tr><td>Upsample</td><td>\( {32} \times  {128} \times  {128} \)</td><td rowspan="2">CAConv (d = 3, r = 2)</td><td rowspan="2">\( {16} \times  {256} \times  {256} \)</td></tr><tr><td>FFM</td><td>\( {32} \times  {128} \times  {128} \)</td></tr><tr><td>CSConv</td><td>\( {32} \times  {128} \times  {128} \)</td><td rowspan="2">CAConv (d = 5, r = 2)</td><td rowspan="2">\( {16} \times  {256} \times  {256} \)</td></tr><tr><td>Upsample</td><td>\( {16} \times  {256} \times  {256} \)</td></tr><tr><td>FFM</td><td>\( {16} \times  {256} \times  {256} \)</td><td rowspan="2">CAConv (d = 7, r = 2)</td><td rowspan="2">\( {16} \times  {256} \times  {256} \)</td></tr><tr><td>CSConv</td><td>\( {16} \times  {256} \times  {256} \)</td></tr><tr><td colspan="4">FFM(Size: \( {16} \times  {256} \times  {256} \) )</td></tr><tr><td colspan="2">Seg Head Module</td><td colspan="2">(Size: \( 2 \times  {512} \times  {512} \) )</td></tr></table>

![bo_d1svja3ef24c73amk3pg_3_277_181_1158_410_0.jpg](images/bo_d1svja3ef24c73amk3pg_3_277_181_1158_410_0.jpg)

FIGURE 3. The architecture of channel aggregation convolution and channel aggregation module. (a)Channel aggregation convolution. (b) Channel aggregation module.

![bo_d1svja3ef24c73amk3pg_3_424_670_858_489_0.jpg](images/bo_d1svja3ef24c73amk3pg_3_424_670_858_489_0.jpg)

FIGURE 4. The architecture of channel split convolution.

## C. CHANNEL SPLIT CONVOLUTION

The high similarity between convolution kernels is the most direct reason for the high similarity between feature maps of different channels. Inspired by GhostNet [39], we propose the channel split convolution, as shown in Figure 4, which adopts different convolutions for different channels. First, the input feature maps are equally split into four groups on the channel dimension and obtain \( \left\{  {{f}_{1},{f}_{2},{f}_{3},{f}_{4}}\right\} \) , then the first group of feature maps \( {f}_{1} \) is processed by adopting a \( 1 \times  1 \) convolution to obtain the processed feature maps \( {f}_{1}^{\prime } \) . Then add to the next group of feature maps \( {f}_{2} \) , and adopt a \( 3 \times  3 \) convolution to process the added feature maps to obtain \( {f}_{2}^{\prime } \) . Repeat this step until all the split feature maps pass this process to obtain the processed feature maps \( \left\{  {{f}_{1}^{\prime },{f}_{2}^{\prime },{f}_{3}^{\prime },{f}_{4}^{\prime }}\right\} \) . We concatenate the processed feature maps on the channel dimension, adopt pointwise convolution to fuse the feature maps, and finally add them to the input feature maps to obtain final result. Channel split convolution can reduce the similarity between feature maps to a certain extent. In addition, by reusing multi-scale information, multi-scale feature maps are extracted while enhancing the equivalent receptive field of CNN.

## D. FEATURE FUSION MODULE

Effective fusion of feature maps at different levels can significantly improve network performance. Direct addition or concatenation operations cannot highlight the important differences between feature maps at different levels. Therefore, we propose an effective feature fusion module, as shown in Fig. 5. SKNet effectively fuses multi-scale feature information by selectively applying convolution kernels of different scales [40], which helps the network capture visual features at different levels and improves the representation ability of features. Inspired by this, we propose a new feature fusion module, that can fuse feature maps at different levels in both channel and spatial dimensions. In the channel dimension, global average pooling is adopted to obtain the global spatial information of the two input feature maps, then the global information is stacked. The Softmax function is adopted to adaptively select spatial information of different scales, and the final result is multiplied by the input feature maps. The fusion method in the spatial dimension is similar to the fusion method in the channel dimension. The difference is that we adopt \( 1 \times  1 \) convolution to obtain the position information of the two input feature maps, and then the position information is stacked. The Softmax function is adopted to adaptively select position information of different scales, and the final result is multiplied by the input feature maps. In general, the proposed feature fusion module achieves the fusion of information at different levels with fewer parameters and computational complexity.

![bo_d1svja3ef24c73amk3pg_4_266_188_1181_389_0.jpg](images/bo_d1svja3ef24c73amk3pg_4_266_188_1181_389_0.jpg)

FIGURE 5. The architecture of feature fusion module.

## III. EXPERIMENT A. DATASETS

![bo_d1svja3ef24c73amk3pg_4_121_1284_692_365_0.jpg](images/bo_d1svja3ef24c73amk3pg_4_121_1284_692_365_0.jpg)

FIGURE 6. The images in the datasets. (a)The dataset of Curiosity. (b) The dataset of Perseverance.

All deep learning models need to be trained on a dataset, the models generate accurate opinions and predictions by learning the inherent rules and presentation levels of the sample data in the dataset. The dataset of Curiosity produced in this paper was taken by the Curiosity rover in Gale Crater. We adopt Furlán's method for data augmentation. The training set has 1100 images and the validation set has 144 images. The dataset of Perseverance produced in this paper was taken by the Perseverance rover in Jezero Crater. After data augmentation, the training set has 1168 images and the validation set has 124 images. We identify rocks that may have an impact on the safety of the rover, so we only marked and segmented rocks with significant features and large sizes, as shown in the red area in Fig. 6. We did not mark and segment those rocks with fewer features and very small sizes, as shown in the yellow box in Figure 6. The resolution of all images is \( {560} \times  {560} \) .

## B. IMPLEMENTATION DETAILS

We conduct experiments based on Pytorch on a single NVIDIA RTX 4090 GPU. The batch size is set to 8 on the dataset of Curiosity and Perseverance, and the sum of cross entropy function and the dice function is adopted as the loss function. We adopt the stochastic gradient descent (SGD) with a momentum of 0.9 , and weight decay 0.0001 to train the model. During the training phase, Data augmentation contains random scaling between 0.5 and 1.2, random horizontal flip, and random vertical flip. The evaluation metrics adopted in the Martian surface rock segmentation task are as follows: mean pixel intersection over union (mIoU), mPA, frames per second (FPS), number of model parameters, and number of floating-point operations (FLOPs).

## C. EVALUATION RESULTS ON THE DATASET OF CURIOSITY

TABLE 2. Comparison with other methods on the dataset of curiosity.

<table><tr><td>Model</td><td>mPA</td><td>MIoU</td><td>FPS</td><td>FLOPs</td><td>Params</td></tr><tr><td>Furlán[16]</td><td>96.8%</td><td>89.40%</td><td>88.6</td><td>44.3G</td><td>17.12M</td></tr><tr><td>Li[18]</td><td>97.0%</td><td>\( \mathbf{{89.52}}\% \)</td><td>77.1</td><td>303.7G</td><td>63.08 M</td></tr><tr><td>Kuang[19]</td><td>95.7%</td><td>86.38%</td><td>25.5</td><td>732.0G</td><td>25.46 M</td></tr><tr><td>Swan[20]</td><td>96.1%</td><td>87.22%</td><td>101.1</td><td>83.1G</td><td>\( {54.71}\mathrm{M} \)</td></tr><tr><td>Lv[21]</td><td>96.3%</td><td>88.01%</td><td>86.6</td><td>272.2G</td><td>102.57M</td></tr><tr><td>Chiodini[22]</td><td>95.8%</td><td>86.54%</td><td>102.5</td><td>89.0G</td><td>59.30M</td></tr><tr><td>LBNet</td><td>96.5%</td><td>88.62%</td><td>152.5</td><td>\( \mathbf{{2.1G}} \)</td><td>\( \mathbf{{0.37M}} \)</td></tr></table>

We compare the proposed model with some commonly adopted deep learning-based Martian rock segmentation models on the dataset of Curiosity. Table 2 gives the comparison results of the number of parameters, FLOPs, \( \mathrm{{mIoU}},\mathrm{{mPA}} \) , and inference speed of different models. It is not difficult to see that our model is superior in inference speed, parameters, and FLOPs while achieving competitive segmentation accuracy. Among all the models, our model has only \( {0.37}\mathrm{M} \) parameters, an inference speed of 152.5, and a mIoU of \( {88.62}\% \) . In existing work, Furlán’s network parameters are the smallest. Compared with it, our model has fewer parameters and a faster inference speed. Our parameters are only \( 1/{45} \) of its, FLOPs are only \( 1/{21} \) of its, the inference speed is about 1.8 times its, and the mIoU has decreased by \( {0.78}\% \) , the little sacrifice of accuracy is worth it. Compared with large networks (Li's model, Kuang's model, Swan's model, Lv's model, and Chiodini's model), our model is still competitive. Li's network has the highest mIoU. Compared with it, our model's mIoU drops by 0.9%, but the parameters, FLOPs, and inference speed have made great progress. Our parameters are only \( 1/{170} \) of its, FLOPs are only \( 1/{144} \) of its and the inference speed is 2 times its. In addition, compared with Kuang's network, Lv's network, and Chiodini's network, our model is more accurate, at the same time, our model has fewer parameters, FLOPs, and faster inference speed.

![bo_d1svja3ef24c73amk3pg_5_216_181_1281_477_0.jpg](images/bo_d1svja3ef24c73amk3pg_5_216_181_1281_477_0.jpg)

FIGURE 7. The visual comparison on the dataset of Curiosity. (a) Input. (b) Ground Truth (c) Furlán’s model. (d) Li’s model. (e) Kuang’s model. (f) Swan. (g) Lv’s model. (h) Chiodini’s model. (i) LBNet.

## D. EVALUATION RESULTS ON THE DATASET OF PERSEVERANCE

TABLE 3. Comparison with other methods on the dataset of Perseverance.

<table><tr><td>Model</td><td>mPA</td><td>MIoU</td><td>FPS</td><td>FLOPs</td><td>Params</td></tr><tr><td>Furlán[16]</td><td>98.5%</td><td>93.78%</td><td>89.5</td><td>44.3G</td><td>17.12M</td></tr><tr><td>Li[18]</td><td>98.7%</td><td>94.50%</td><td>77.5</td><td>303.7G</td><td>63.08M</td></tr><tr><td>Kuang[19]</td><td>98.3%</td><td>92.73%</td><td>25.6</td><td>732.0G</td><td>25.46M</td></tr><tr><td>Swan[20]</td><td>98.4%</td><td>93.41%</td><td>98.5</td><td>83.1G</td><td>54.71M</td></tr><tr><td>Lv[21]</td><td>98.5%</td><td>92.75%</td><td>88.3</td><td>272.2G</td><td>102.57M</td></tr><tr><td>Chiodini[22]</td><td>98.3%</td><td>92.94%</td><td>97.2</td><td>89G</td><td>59.30M</td></tr><tr><td>LBNet</td><td>98.5%</td><td>93.85%</td><td>147.8</td><td>\( \mathbf{{2.1G}} \)</td><td>\( \mathbf{{0.37M}} \)</td></tr></table>

To further evaluate the effectiveness of the proposed network model, we conduct experiments on the Perseverance dataset. The results are shown in Table 3. From the results in the table, we can see that our model has also achieved a good balance in parameters, FLOPs, accuracy, and inference speed on the Perseverance dataset.

## E. ABLATION STUDY

To verify the effectiveness of each module, we conduct ablation experiments on each module on the dataset of Perseverance. The results of the experiment are shown in Table 4 and Table 5.

From the data in the first two rows of the table 4, we can see that without adopting the spatial detail branch, the prediction effect of the network is worse. The spatial detail branch can improve mIoU by \( {0.96}\% \) . The dense connection CAConv does not bring more parameters and computational complexity to the network. The experiment proves the effectiveness of the spatial detail branch composed of dense connection CAConv.

Aggregation rate directly affects CAConv performance. To test the impact of different aggregation rates on CAConv, we conduct a series of ablation experiments. From the data in the table, we can see that compared with a larger aggregation rate, when the aggregation rate is 2 , the network has the best performance. At this time, the network parameters have not increased significantly, and the increase in the amount of network computation is within an acceptable range.

In the spatial detail branch, the dilation rate of the dilated convolution in CAConv has a certain impact on the network performance. To obtain the best performance, we conduct a series of ablation experiments on the dilation rate of the atrous convolution in CAConv, and adopt different dilate rates for the dilated convolution in the dense connection CAConv. From the data in the table, when the dilation rates of the atrous convolution in dense connection CAConv are1,3,5, and 7 respectively, the network can achieve the best performance.

We propose an effective feature fusion method FFM to fuse feature maps at different levels. To verify the effectiveness of this method, we compare it with the widely adopted "Add" and "Concatenate" operations. From the data in the table, we can see that the proposed FFM achieves the best results, with mIoU increased by 1.04% and 0.63% respectively compared with "Add" and "Concatenate" operations. Compared with "Add" and "Concatenate" operations, the parameters and calculation amount of FFM did not increase significantly, which is acceptable.

TABLE 4. Ablation experiments on the dataset of Perseverance.

<table><tr><td rowspan="2">Model</td><td rowspan="2">SIB</td><td rowspan="2">SDB</td><td rowspan="2">CAConv</td><td colspan="3">Fusion</td><td rowspan="2">Params (M)</td><td rowspan="2">FLOPs (G)</td><td rowspan="2">MIoU (%)</td></tr><tr><td>Add</td><td>Concatenate</td><td>FFM</td></tr><tr><td colspan="10">(a)Bilateral Net</td></tr><tr><td>LBNet</td><td>✓</td><td/><td/><td/><td/><td>✓</td><td/><td/><td>92.89</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>d=(1,3,5,7)</td><td/><td/><td>✓</td><td>0.372</td><td>2.36</td><td>93.85</td></tr><tr><td colspan="10">(b) CAConv for aggregation rate</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>r=2</td><td/><td/><td>✓</td><td>0.372</td><td>2.36</td><td>93.85</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>r=4</td><td/><td/><td>✓</td><td>0.368</td><td>2.10</td><td>93.45</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>r=8</td><td/><td/><td>✓</td><td>0.367</td><td>2.02</td><td>93.16</td></tr><tr><td colspan="10">(c) CAConv for dilated rate</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>\( \mathrm{d} = \left( {1,1,1,1}\right) \)</td><td/><td/><td>✓</td><td>0.372</td><td>2.36</td><td>93.36</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>\( \mathrm{d} = \left( {1,2,4,6}\right) \)</td><td/><td/><td>✓</td><td>0.372</td><td>2.36</td><td>93.58</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>\( \mathrm{d} = \left( {1,3,5,7}\right) \)</td><td/><td/><td>✓</td><td>0.372</td><td>2.36</td><td>93.85</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>\( \mathrm{d} = \left( {1,3,6,9}\right) \)</td><td/><td/><td>✓</td><td>0.372</td><td>2.36</td><td>93.29</td></tr><tr><td colspan="10">(d) FFM</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>d=(1,3,5,7)</td><td>✓</td><td/><td/><td>0.372</td><td>2.35</td><td>92.81</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>d=(1,3,5,7)</td><td/><td>✓</td><td/><td>0.383</td><td>2.49</td><td>93.22</td></tr><tr><td>LBNet</td><td>✓</td><td>✓</td><td>d=(1,3,5,7)</td><td/><td/><td>✓</td><td>0.372</td><td>2.36</td><td>93.85</td></tr></table>

TABLE 5. Comparison with other modules on the dataset of Perseverance.

<table><tr><td>Model</td><td>mPA</td><td>MIoU</td><td>FPS</td><td>FLOPs</td><td>Params</td></tr><tr><td>Partial convolution (Partial ratio=1/2)</td><td>98.0%</td><td>91.70%</td><td>150.2</td><td>2.3G</td><td>0.37M</td></tr><tr><td>Ghost module</td><td>98.1%</td><td>91.88%</td><td>145.7</td><td>2.1G</td><td>0.37M</td></tr><tr><td>CAConv (Aggregation rate = 1/2)</td><td>98.5%</td><td>93.85%</td><td>147.8</td><td>2.1G</td><td>0.37M</td></tr></table>

We replace the CAConv in the LBNet network with Partial convolution and Ghost module and conducte comparative experiments. The experimental results are shown in Table 5. From the data in the table 5, we can see that compared with Partial Convolution and Ghost module, our model has better accuracy in the Mars rock segmentation mission while maintaining the inference speed.

## IV. CONCLUSION

In this paper, we propose a lightweight bilateral network for semantic segmentation of Martian rock. Through carefully designed modules, our model achieves a relatively good balance between accuracy, model size, and inference speed. The proposed channel aggregation convolution can make good use of the similarity between feature layers to reduce calculations. Channel split convolution reduces the similarity between feature layers and extracts multi-scale features by adopting different convolutions for different feature maps. Feature aggregation module can well fuse feature maps at different levels. Large number of experiments show that our proposed model achieves a good balance between model accuracy and efficiency.

