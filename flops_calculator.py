#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FLOPs Calculator for Deep Learning Models
专业的深度学习模型FLOPs计算工具

Author: AI Assistant
Date: 2024
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Dict, Any

class FLOPsCalculator:
    """
    专业的FLOPs计算器，支持多种深度学习层的精确计算
    """
    
    def __init__(self):
        self.total_flops = 0
        self.layer_flops = {}
        self.hooks = []
        
    def calculate_flops(self, model: nn.Module, input_shape: Tuple[int, ...], 
                       device: str = 'cpu') -> Dict[str, Any]:
        """
        计算模型的FLOPs
        
        Args:
            model: PyTorch模型
            input_shape: 输入张量形状 (batch_size, channels, height, width)
            device: 计算设备
            
        Returns:
            包含FLOPs信息的字典
        """
        self.total_flops = 0
        self.layer_flops = {}
        self.hooks = []
        
        # 创建输入张量
        dummy_input = torch.randn(*input_shape).to(device)
        model = model.to(device)
        model.eval()
        
        # 注册钩子函数
        self._register_hooks(model)
        
        # 前向传播
        with torch.no_grad():
            _ = model(dummy_input)
        
        # 移除钩子
        self._remove_hooks()
        
        return {
            'total_flops': self.total_flops,
            'total_flops_G': self.total_flops / 1e9,
            'layer_flops': self.layer_flops,
            'input_shape': input_shape
        }
    
    def _register_hooks(self, model: nn.Module):
        """注册钩子函数到所有层"""
        for name, module in model.named_modules():
            if len(list(module.children())) == 0:  # 叶子节点
                hook = module.register_forward_hook(
                    lambda module, input, output, name=name: self._flop_count_hook(
                        module, input, output, name
                    )
                )
                self.hooks.append(hook)
    
    def _remove_hooks(self):
        """移除所有钩子"""
        for hook in self.hooks:
            hook.remove()
        self.hooks = []
    
    def _flop_count_hook(self, module: nn.Module, input: Tuple, output: torch.Tensor, name: str):
        """计算单个层的FLOPs"""
        flops = 0
        
        if isinstance(module, nn.Conv2d):
            flops = self._conv2d_flops(module, input[0], output)
        elif isinstance(module, nn.ConvTranspose2d):
            flops = self._conv_transpose2d_flops(module, input[0], output)
        elif isinstance(module, nn.BatchNorm2d):
            flops = self._batchnorm2d_flops(module, input[0], output)
        elif isinstance(module, nn.ReLU) or isinstance(module, nn.ReLU6):
            flops = self._relu_flops(module, input[0], output)
        elif isinstance(module, nn.MaxPool2d) or isinstance(module, nn.AvgPool2d):
            flops = self._pool2d_flops(module, input[0], output)
        elif isinstance(module, nn.AdaptiveAvgPool2d):
            flops = self._adaptive_pool2d_flops(module, input[0], output)
        elif isinstance(module, nn.Linear):
            flops = self._linear_flops(module, input[0], output)
        elif isinstance(module, nn.Upsample):
            flops = self._upsample_flops(module, input[0], output)
        elif hasattr(module, 'weight') and hasattr(module, 'bias'):
            # 通用计算方法
            flops = self._generic_flops(module, input[0], output)
        
        self.total_flops += flops
        self.layer_flops[name] = flops
    
    def _conv2d_flops(self, module: nn.Conv2d, input_tensor: torch.Tensor, 
                     output_tensor: torch.Tensor) -> int:
        """计算Conv2d层的FLOPs"""
        batch_size = input_tensor.shape[0]
        output_dims = output_tensor.shape[2:]
        kernel_dims = module.kernel_size
        in_channels = module.in_channels
        out_channels = module.out_channels
        groups = module.groups
        
        filters_per_channel = out_channels // groups
        conv_per_position_flops = int(np.prod(kernel_dims)) * in_channels // groups
        
        active_elements_count = batch_size * int(np.prod(output_dims))
        overall_conv_flops = conv_per_position_flops * active_elements_count * filters_per_channel
        
        # 添加偏置项
        bias_flops = 0
        if module.bias is not None:
            bias_flops = out_channels * active_elements_count
        
        overall_flops = overall_conv_flops + bias_flops
        return overall_flops
    
    def _conv_transpose2d_flops(self, module: nn.ConvTranspose2d, input_tensor: torch.Tensor,
                               output_tensor: torch.Tensor) -> int:
        """计算ConvTranspose2d层的FLOPs"""
        batch_size = input_tensor.shape[0]
        input_dims = input_tensor.shape[2:]
        kernel_dims = module.kernel_size
        in_channels = module.in_channels
        out_channels = module.out_channels
        groups = module.groups
        
        filters_per_channel = in_channels // groups
        conv_per_position_flops = int(np.prod(kernel_dims)) * out_channels // groups
        
        active_elements_count = batch_size * int(np.prod(input_dims))
        overall_conv_flops = conv_per_position_flops * active_elements_count * filters_per_channel
        
        # 添加偏置项
        bias_flops = 0
        if module.bias is not None:
            bias_flops = out_channels * batch_size * int(np.prod(output_tensor.shape[2:]))
        
        overall_flops = overall_conv_flops + bias_flops
        return overall_flops
    
    def _batchnorm2d_flops(self, module: nn.BatchNorm2d, input_tensor: torch.Tensor,
                          output_tensor: torch.Tensor) -> int:
        """计算BatchNorm2d层的FLOPs"""
        # BatchNorm: 2 operations per element (subtract mean, divide by std)
        return input_tensor.numel() * 2
    
    def _relu_flops(self, module: nn.Module, input_tensor: torch.Tensor,
                   output_tensor: torch.Tensor) -> int:
        """计算ReLU层的FLOPs"""
        # ReLU: 1 comparison per element
        return input_tensor.numel()
    
    def _pool2d_flops(self, module: nn.Module, input_tensor: torch.Tensor,
                     output_tensor: torch.Tensor) -> int:
        """计算池化层的FLOPs"""
        if isinstance(module, nn.MaxPool2d):
            # MaxPool: kernel_size comparisons per output element
            kernel_flops = int(np.prod(module.kernel_size))
        else:  # AvgPool2d
            # AvgPool: kernel_size additions + 1 division per output element
            kernel_flops = int(np.prod(module.kernel_size)) + 1
        
        return output_tensor.numel() * kernel_flops
    
    def _adaptive_pool2d_flops(self, module: nn.AdaptiveAvgPool2d, input_tensor: torch.Tensor,
                              output_tensor: torch.Tensor) -> int:
        """计算自适应池化层的FLOPs"""
        # 简化计算：假设每个输出元素需要处理的输入元素数量
        input_size = int(np.prod(input_tensor.shape[2:]))
        output_size = int(np.prod(output_tensor.shape[2:]))
        
        if output_size == 0:
            return 0
        
        kernel_flops = input_size // output_size + 1
        return output_tensor.numel() * kernel_flops
    
    def _linear_flops(self, module: nn.Linear, input_tensor: torch.Tensor,
                     output_tensor: torch.Tensor) -> int:
        """计算Linear层的FLOPs"""
        # Linear: input_features * output_features multiply-adds per sample
        batch_size = input_tensor.shape[0]
        flops = batch_size * module.in_features * module.out_features
        
        # 添加偏置项
        if module.bias is not None:
            flops += batch_size * module.out_features
        
        return flops
    
    def _upsample_flops(self, module: nn.Upsample, input_tensor: torch.Tensor,
                       output_tensor: torch.Tensor) -> int:
        """计算上采样层的FLOPs"""
        if module.mode == 'nearest':
            # Nearest: no computation, just indexing
            return 0
        elif module.mode == 'linear' or module.mode == 'bilinear':
            # Bilinear: 4 multiplications + 3 additions per output element
            return output_tensor.numel() * 7
        else:
            # 其他模式的简化计算
            return output_tensor.numel() * 4
    
    def _generic_flops(self, module: nn.Module, input_tensor: torch.Tensor,
                      output_tensor: torch.Tensor) -> int:
        """通用FLOPs计算方法"""
        # 简化计算：假设每个输出元素需要一次乘法运算
        return output_tensor.numel()

def calculate_model_flops(model: nn.Module, input_shape: Tuple[int, ...], 
                         device: str = 'cpu') -> Dict[str, Any]:
    """
    便捷函数：计算模型FLOPs
    
    Args:
        model: PyTorch模型
        input_shape: 输入形状 (batch_size, channels, height, width)
        device: 计算设备
        
    Returns:
        FLOPs计算结果
    """
    calculator = FLOPsCalculator()
    return calculator.calculate_flops(model, input_shape, device)

# 示例使用
if __name__ == "__main__":
    # 创建一个简单的模型进行测试
    class SimpleModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.conv1 = nn.Conv2d(3, 64, 3, padding=1)
            self.bn1 = nn.BatchNorm2d(64)
            self.relu = nn.ReLU(inplace=True)
            self.conv2 = nn.Conv2d(64, 128, 3, padding=1)
            self.pool = nn.AdaptiveAvgPool2d((1, 1))
            self.fc = nn.Linear(128, 10)
        
        def forward(self, x):
            x = self.relu(self.bn1(self.conv1(x)))
            x = self.conv2(x)
            x = self.pool(x)
            x = x.view(x.size(0), -1)
            x = self.fc(x)
            return x
    
    model = SimpleModel()
    input_shape = (1, 3, 224, 224)
    
    result = calculate_model_flops(model, input_shape)
    print(f"Total FLOPs: {result['total_flops_G']:.2f} G")
    print(f"Layer-wise FLOPs:")
    for layer, flops in result['layer_flops'].items():
        if flops > 0:
            print(f"  {layer}: {flops/1e6:.2f} M")
