#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型效率评估器使用示例
Example usage of model efficiency evaluator

展示如何使用评估器的各种功能

Author: AI Assistant
Date: 2024
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_single_model_evaluation():
    """示例：单个模型评估"""
    print("=" * 60)
    print("示例1: 单个模型评估")
    print("=" * 60)
    
    try:
        from batch_evaluator import BatchEvaluator
        
        evaluator = BatchEvaluator()
        
        # 评估单个模型
        model_name = "deeplabv3plus_resnet50"
        print(f"评估模型: {model_name}")
        
        result = evaluator.evaluate_model(
            model_name=model_name,
            input_size=512,
            batch_size=4,
            num_classes=5
        )
        
        if result:
            print("\n评估结果:")
            print(f"  参数数量: {result['params_M']:.2f} M")
            print(f"  FLOPs: {result['flops_G']:.2f} G")
            print(f"  FPS: {result['fps']:.1f}")
            print(f"  设备: {result['device']}")
        
        return True
        
    except Exception as e:
        print(f"单个模型评估失败: {e}")
        return False

def example_batch_evaluation():
    """示例：批量模型评估"""
    print("\n" + "=" * 60)
    print("示例2: 批量模型评估")
    print("=" * 60)
    
    try:
        from batch_evaluator import BatchEvaluator
        
        evaluator = BatchEvaluator()
        
        # 选择几个轻量级模型进行快速测试
        test_models = [
            "deeplabv3plus_resnet50",
            "deeplabv3plus_mobilenet",
            "unet_resnet50"
        ]
        
        print(f"批量评估模型: {test_models}")
        
        results = evaluator.batch_evaluate(
            model_list=test_models,
            input_size=256,  # 使用较小尺寸加快测试
            batch_size=2,
            num_classes=5
        )
        
        if results:
            print(f"\n批量评估完成，共评估 {len(results)} 个模型")
            
            # 显示对比结果
            print("\n对比结果:")
            print("模型名称".ljust(25) + "参数(M)".ljust(10) + "FLOPs(G)".ljust(10) + "FPS")
            print("-" * 50)
            
            for result in results:
                print(f"{result['model_name']:<25}{result['params_M']:<10.2f}{result['flops_G']:<10.2f}{result['fps']:.1f}")
        
        return True
        
    except Exception as e:
        print(f"批量评估失败: {e}")
        return False

def example_flops_calculator():
    """示例：FLOPs计算器使用"""
    print("\n" + "=" * 60)
    print("示例3: FLOPs计算器使用")
    print("=" * 60)
    
    try:
        import torch
        import torch.nn as nn
        from flops_calculator import calculate_model_flops
        
        # 创建一个自定义模型
        class CustomModel(nn.Module):
            def __init__(self, num_classes=5):
                super().__init__()
                self.features = nn.Sequential(
                    nn.Conv2d(3, 32, 3, padding=1),
                    nn.BatchNorm2d(32),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(32, 64, 3, stride=2, padding=1),
                    nn.BatchNorm2d(64),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(64, 128, 3, stride=2, padding=1),
                    nn.BatchNorm2d(128),
                    nn.ReLU(inplace=True),
                )
                self.classifier = nn.Sequential(
                    nn.AdaptiveAvgPool2d((1, 1)),
                    nn.Flatten(),
                    nn.Linear(128, num_classes)
                )
            
            def forward(self, x):
                x = self.features(x)
                x = self.classifier(x)
                return x
        
        model = CustomModel(num_classes=5)
        input_shape = (1, 3, 512, 512)
        
        print("计算自定义模型的FLOPs...")
        result = calculate_model_flops(model, input_shape, 'cpu')
        
        print(f"总FLOPs: {result['total_flops_G']:.2f} G")
        print(f"输入形状: {result['input_shape']}")
        
        # 显示各层FLOPs
        print("\n各层FLOPs分布:")
        for layer_name, flops in result['layer_flops'].items():
            if flops > 0:
                print(f"  {layer_name}: {flops/1e6:.2f} M")
        
        return True
        
    except Exception as e:
        print(f"FLOPs计算器示例失败: {e}")
        return False

def example_gui_usage():
    """示例：GUI使用说明"""
    print("\n" + "=" * 60)
    print("示例4: GUI界面使用")
    print("=" * 60)
    
    print("GUI界面使用步骤:")
    print("1. 运行命令: python run_evaluator.py")
    print("2. 在GUI界面中:")
    print("   - 选择模型: 从下拉菜单选择要评估的模型")
    print("   - 选择数据集: AI4Mars, AI4Mars-SMI, 或 LabelMars6")
    print("   - 设置输入尺寸: 256, 512, 768, 或 1024")
    print("   - 设置批次大小: 1, 2, 4, 8, 或 16")
    print("   - 点击'开始评估'按钮")
    print("3. 等待评估完成，查看结果报告")
    
    print("\n注意事项:")
    print("- 大模型或大输入尺寸可能需要更多GPU内存")
    print("- 首次运行某些模型时会下载预训练权重")
    print("- 评估过程中请勿关闭程序")
    
    return True

def example_command_line_usage():
    """示例：命令行使用"""
    print("\n" + "=" * 60)
    print("示例5: 命令行使用")
    print("=" * 60)
    
    print("命令行使用示例:")
    print()
    
    print("1. 评估所有模型:")
    print("   python batch_evaluator.py")
    print()
    
    print("2. 评估指定模型:")
    print("   python batch_evaluator.py --models deeplabv3plus_resnet50 unet_resnet50")
    print()
    
    print("3. 自定义参数:")
    print("   python batch_evaluator.py \\")
    print("     --models segformer_b0 segformer_b1 \\")
    print("     --input-size 256 \\")
    print("     --batch-size 8 \\")
    print("     --num-classes 5 \\")
    print("     --output-dir my_results")
    print()
    
    print("4. 测试环境:")
    print("   python test_evaluator.py")
    
    return True

def main():
    """主函数"""
    print("模型效率评估器使用示例")
    print("Model Efficiency Evaluator Usage Examples")
    
    examples = [
        ("单个模型评估", example_single_model_evaluation),
        ("批量模型评估", example_batch_evaluation),
        ("FLOPs计算器", example_flops_calculator),
        ("GUI使用说明", example_gui_usage),
        ("命令行使用", example_command_line_usage),
    ]
    
    success_count = 0
    
    for name, example_func in examples:
        try:
            if example_func():
                success_count += 1
        except KeyboardInterrupt:
            print("\n用户中断")
            break
        except Exception as e:
            print(f"示例 '{name}' 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print("示例演示完成")
    print("=" * 60)
    
    if success_count > 0:
        print("🎉 评估器可以正常使用!")
        print("\n快速开始:")
        print("  python run_evaluator.py          # 启动GUI")
        print("  python test_evaluator.py         # 测试环境")
        print("  python batch_evaluator.py        # 批量评估")
    else:
        print("⚠️  请先解决环境问题")

if __name__ == "__main__":
    main()
