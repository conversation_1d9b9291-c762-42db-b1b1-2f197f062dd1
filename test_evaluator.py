#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估器测试脚本
Test script for model evaluator

用于测试评估器的基本功能是否正常

Author: AI Assistant
Date: 2024
"""

import sys
import os
import torch
import torch.nn as nn

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_flops_calculator():
    """测试FLOPs计算器"""
    print("测试FLOPs计算器...")
    
    try:
        from flops_calculator import FLOPsCalculator
        
        # 创建一个简单的测试模型
        class TestModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.conv1 = nn.Conv2d(3, 64, 3, padding=1)
                self.bn1 = nn.BatchNorm2d(64)
                self.relu = nn.ReLU(inplace=True)
                self.conv2 = nn.Conv2d(64, 128, 3, padding=1)
                self.pool = nn.AdaptiveAvgPool2d((1, 1))
                self.fc = nn.Linear(128, 10)
            
            def forward(self, x):
                x = self.relu(self.bn1(self.conv1(x)))
                x = self.conv2(x)
                x = self.pool(x)
                x = x.view(x.size(0), -1)
                x = self.fc(x)
                return x
        
        model = TestModel()
        calculator = FLOPsCalculator()
        
        input_shape = (1, 3, 224, 224)
        result = calculator.calculate_flops(model, input_shape, 'cpu')
        
        print(f"  ✅ FLOPs计算成功: {result['total_flops_G']:.2f} G")
        return True
        
    except Exception as e:
        print(f"  ❌ FLOPs计算器测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("测试模型创建...")
    
    try:
        import network
        from backboned_unet import Unet
        
        # 测试DeepLabV3+模型
        model1 = network.modeling.deeplabv3plus_resnet50(num_classes=5, output_stride=16)
        print(f"  ✅ DeepLabV3+ ResNet50创建成功")
        
        # 测试U-Net模型
        model2 = Unet(backbone_name='resnet50', classes=5)
        print(f"  ✅ U-Net ResNet50创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模型创建测试失败: {e}")
        return False

def test_parameter_counting():
    """测试参数计算"""
    print("测试参数计算...")
    
    try:
        # 创建简单模型
        model = nn.Sequential(
            nn.Conv2d(3, 64, 3),
            nn.ReLU(),
            nn.Conv2d(64, 128, 3),
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(128, 10)
        )
        
        params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"  ✅ 参数计算成功: {params:,} 个参数")
        return True
        
    except Exception as e:
        print(f"  ❌ 参数计算测试失败: {e}")
        return False

def test_fps_measurement():
    """测试FPS测量"""
    print("测试FPS测量...")
    
    try:
        import time
        
        # 创建简单模型
        model = nn.Sequential(
            nn.Conv2d(3, 32, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(32, 64, 3, padding=1),
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(64, 5)
        )
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model.eval()
        
        # 测试推理
        dummy_input = torch.randn(1, 3, 256, 256).to(device)
        
        # 预热
        with torch.no_grad():
            for _ in range(5):
                _ = model(dummy_input)
        
        # 测量时间
        num_iterations = 20
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_iterations):
                _ = model(dummy_input)
        
        if device.type == 'cuda':
            torch.cuda.synchronize()
        
        end_time = time.time()
        total_time = end_time - start_time
        fps = num_iterations / total_time
        
        print(f"  ✅ FPS测量成功: {fps:.1f} FPS (设备: {device})")
        return True
        
    except Exception as e:
        print(f"  ❌ FPS测量测试失败: {e}")
        return False

def test_gui_imports():
    """测试GUI相关导入"""
    print("测试GUI导入...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        print("  ✅ tkinter导入成功")
        
        # 测试创建窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("  ✅ GUI窗口创建测试成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ GUI导入测试失败: {e}")
        return False

def test_project_structure():
    """测试项目结构"""
    print("测试项目结构...")
    
    required_files = [
        'network/__init__.py',
        'network/modeling.py',
        'datasets/__init__.py',
        'utils/__init__.py',
        'backboned_unet/__init__.py',
        'model_efficiency_evaluator.py',
        'flops_calculator.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"  ❌ 缺少文件: {missing_files}")
        return False
    else:
        print("  ✅ 项目结构完整")
        return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("模型效率评估器测试")
    print("=" * 60)
    
    tests = [
        ("项目结构", test_project_structure),
        ("GUI导入", test_gui_imports),
        ("模型创建", test_model_creation),
        ("参数计算", test_parameter_counting),
        ("FLOPs计算器", test_flops_calculator),
        ("FPS测量", test_fps_measurement),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 评估器可以正常使用")
        print("\n使用方法:")
        print("  python run_evaluator.py          # 启动GUI界面")
        print("  python batch_evaluator.py        # 批量评估所有模型")
        print("  python model_efficiency_evaluator.py  # 直接启动GUI")
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        print("\n常见解决方案:")
        print("  1. 确保在项目根目录下运行")
        print("  2. 安装缺失的依赖包")
        print("  3. 检查CUDA环境配置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
