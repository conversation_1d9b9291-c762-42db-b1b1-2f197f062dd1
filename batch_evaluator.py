#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量模型效率评估器
Batch Model Efficiency Evaluator

用于批量评估多个模型的效率，生成对比报告

Author: AI Assistant
Date: 2024
"""

import sys
import os
import time
import json
import csv
from datetime import datetime
import torch
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import network
    from backboned_unet import Unet
    from flops_calculator import FLOPsCalculator
    import psutil
    import platform
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

class BatchEvaluator:
    """批量模型评估器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.results = []
        
    def get_available_models(self):
        """获取可用模型列表"""
        models = []
        
        # 获取network.modeling中的模型
        for name in dir(network.modeling):
            if (name.islower() and 
                not name.startswith("_") and 
                callable(getattr(network.modeling, name))):
                models.append(name)
        
        # 添加U-Net模型
        models.extend(["unet_resnet50", "unet_resnet101"])
        
        return sorted(models)
    
    def create_model(self, model_name, num_classes=5):
        """创建模型实例"""
        try:
            if model_name.startswith("unet_"):
                backbone_name = model_name.split("_")[1]
                model = Unet(backbone_name=backbone_name, classes=num_classes)
            else:
                model_func = getattr(network.modeling, model_name)
                model = model_func(num_classes=num_classes, output_stride=16)
            
            return model.to(self.device)
        except Exception as e:
            print(f"创建模型 {model_name} 失败: {e}")
            return None
    
    def count_parameters(self, model):
        """计算模型参数数量"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    def calculate_flops(self, model, input_size, batch_size):
        """计算FLOPs"""
        try:
            calculator = FLOPsCalculator()
            input_shape = (batch_size, 3, input_size, input_size)
            result = calculator.calculate_flops(model, input_shape, str(self.device))
            return result['total_flops']
        except Exception as e:
            print(f"FLOPs计算失败: {e}")
            return 0
    
    def measure_fps(self, model, input_size, batch_size, num_iterations=50):
        """测量FPS"""
        try:
            model.eval()
            dummy_input = torch.randn(batch_size, 3, input_size, input_size).to(self.device)
            
            # 预热
            with torch.no_grad():
                for _ in range(5):
                    _ = model(dummy_input)
            
            # 同步GPU
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
            
            # 测量时间
            start_time = time.time()
            with torch.no_grad():
                for _ in range(num_iterations):
                    _ = model(dummy_input)
            
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
            
            end_time = time.time()
            total_time = end_time - start_time
            fps = (num_iterations * batch_size) / total_time
            
            return fps
        except Exception as e:
            print(f"FPS测试失败: {e}")
            return 0
    
    def evaluate_model(self, model_name, input_size=512, batch_size=4, num_classes=5):
        """评估单个模型"""
        print(f"正在评估模型: {model_name}")
        
        # 创建模型
        model = self.create_model(model_name, num_classes)
        if model is None:
            return None
        
        try:
            # 计算参数数量
            params = self.count_parameters(model)
            print(f"  参数数量: {params/1e6:.2f}M")
            
            # 计算FLOPs
            flops = self.calculate_flops(model, input_size, batch_size)
            print(f"  FLOPs: {flops/1e9:.2f}G")
            
            # 测量FPS
            fps = self.measure_fps(model, input_size, batch_size)
            print(f"  FPS: {fps:.1f}")
            
            result = {
                'model_name': model_name,
                'input_size': input_size,
                'batch_size': batch_size,
                'num_classes': num_classes,
                'params': params,
                'params_M': params / 1e6,
                'flops': flops,
                'flops_G': flops / 1e9,
                'fps': fps,
                'device': str(self.device),
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            print(f"评估模型 {model_name} 时出错: {e}")
            return None
        finally:
            # 清理内存
            del model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
    
    def batch_evaluate(self, model_list=None, input_size=512, batch_size=4, num_classes=5):
        """批量评估模型"""
        if model_list is None:
            model_list = self.get_available_models()
        
        print(f"开始批量评估 {len(model_list)} 个模型")
        print(f"配置: 输入尺寸={input_size}, 批次大小={batch_size}, 类别数={num_classes}")
        print(f"设备: {self.device}")
        print("-" * 60)
        
        self.results = []
        
        for i, model_name in enumerate(model_list, 1):
            print(f"[{i}/{len(model_list)}] ", end="")
            result = self.evaluate_model(model_name, input_size, batch_size, num_classes)
            
            if result:
                self.results.append(result)
            
            print()
        
        print("批量评估完成!")
        return self.results
    
    def save_results(self, output_dir="evaluation_results"):
        """保存评估结果"""
        if not self.results:
            print("没有评估结果可保存")
            return
        
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON格式
        json_file = os.path.join(output_dir, f"evaluation_results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到: {json_file}")
        
        # 保存CSV格式
        csv_file = os.path.join(output_dir, f"evaluation_results_{timestamp}.csv")
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if self.results:
                writer = csv.DictWriter(f, fieldnames=self.results[0].keys())
                writer.writeheader()
                writer.writerows(self.results)
        print(f"结果已保存到: {csv_file}")
        
        # 生成对比报告
        self.generate_comparison_report(output_dir, timestamp)
    
    def generate_comparison_report(self, output_dir, timestamp):
        """生成对比报告"""
        if not self.results:
            return
        
        report_file = os.path.join(output_dir, f"comparison_report_{timestamp}.md")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 模型效率对比报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 硬件信息
            f.write("## 硬件环境\n\n")
            f.write(f"- 操作系统: {platform.system()} {platform.release()}\n")
            f.write(f"- 处理器: {platform.processor()}\n")
            memory = psutil.virtual_memory()
            f.write(f"- 内存: {memory.total / (1024**3):.1f} GB\n")
            if torch.cuda.is_available():
                f.write(f"- GPU: {torch.cuda.get_device_name(0)}\n")
                f.write(f"- GPU内存: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB\n")
            f.write(f"- PyTorch: {torch.__version__}\n\n")
            
            # 评估配置
            if self.results:
                sample = self.results[0]
                f.write("## 评估配置\n\n")
                f.write(f"- 输入尺寸: {sample['input_size']}×{sample['input_size']}\n")
                f.write(f"- 批次大小: {sample['batch_size']}\n")
                f.write(f"- 类别数量: {sample['num_classes']}\n\n")
            
            # 结果表格
            f.write("## 评估结果\n\n")
            f.write("| 模型 | 参数(M) | FLOPs(G) | FPS | 效率评级 |\n")
            f.write("|------|---------|----------|-----|----------|\n")
            
            # 按FPS排序
            sorted_results = sorted(self.results, key=lambda x: x['fps'], reverse=True)
            
            for result in sorted_results:
                # 计算效率评级
                rating = self.calculate_efficiency_rating(result)
                f.write(f"| {result['model_name']} | {result['params_M']:.2f} | "
                       f"{result['flops_G']:.2f} | {result['fps']:.1f} | {rating} |\n")
            
            f.write("\n## 效率分析\n\n")
            
            # 最佳模型分析
            best_fps = max(self.results, key=lambda x: x['fps'])
            best_params = min(self.results, key=lambda x: x['params'])
            best_flops = min(self.results, key=lambda x: x['flops'])
            
            f.write(f"- **最快推理**: {best_fps['model_name']} ({best_fps['fps']:.1f} FPS)\n")
            f.write(f"- **最少参数**: {best_params['model_name']} ({best_params['params_M']:.2f}M)\n")
            f.write(f"- **最低FLOPs**: {best_flops['model_name']} ({best_flops['flops_G']:.2f}G)\n")
        
        print(f"对比报告已保存到: {report_file}")
    
    def calculate_efficiency_rating(self, result):
        """计算效率评级"""
        score = 0
        
        # 参数评分
        if result['params_M'] < 1:
            score += 3
        elif result['params_M'] < 10:
            score += 2
        else:
            score += 1
        
        # FLOPs评分
        if result['flops_G'] < 5:
            score += 3
        elif result['flops_G'] < 50:
            score += 2
        else:
            score += 1
        
        # FPS评分
        if result['fps'] > 100:
            score += 3
        elif result['fps'] > 30:
            score += 2
        else:
            score += 1
        
        # 评级映射
        if score >= 8:
            return "⭐⭐⭐"
        elif score >= 6:
            return "⭐⭐"
        else:
            return "⭐"

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量模型效率评估器")
    parser.add_argument("--models", nargs="+", help="要评估的模型列表")
    parser.add_argument("--input-size", type=int, default=512, help="输入图像尺寸")
    parser.add_argument("--batch-size", type=int, default=4, help="批次大小")
    parser.add_argument("--num-classes", type=int, default=5, help="类别数量")
    parser.add_argument("--output-dir", default="evaluation_results", help="输出目录")
    
    args = parser.parse_args()
    
    evaluator = BatchEvaluator()
    
    # 获取模型列表
    if args.models:
        model_list = args.models
    else:
        model_list = evaluator.get_available_models()
        print(f"将评估所有可用模型: {len(model_list)} 个")
    
    # 批量评估
    results = evaluator.batch_evaluate(
        model_list=model_list,
        input_size=args.input_size,
        batch_size=args.batch_size,
        num_classes=args.num_classes
    )
    
    # 保存结果
    evaluator.save_results(args.output_dir)
    
    print(f"\n评估完成! 共评估了 {len(results)} 个模型")

if __name__ == "__main__":
    main()
