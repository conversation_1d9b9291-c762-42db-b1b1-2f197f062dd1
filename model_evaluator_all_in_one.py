#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火星地形分割模型效率评估器 - 完整版
Mars Terrain Segmentation Model Efficiency Evaluator - All-in-One

功能包括:
- GUI界面评估
- 批量命令行评估
- 专业FLOPs计算
- 硬件环境检测
- 详细报告生成

使用方法:
    python model_evaluator_all_in_one.py                    # 启动GUI
    python model_evaluator_all_in_one.py --batch            # 批量评估
    python model_evaluator_all_in_one.py --test             # 测试环境
    python model_evaluator_all_in_one.py --help             # 显示帮助

Author: AI Assistant
Date: 2024
"""

import sys
import os
import time
import json
import csv
import platform
import psutil
import argparse
from datetime import datetime
from typing import Tuple, Dict, Any
import threading
from collections import OrderedDict

import torch
import torch.nn as nn
import numpy as np

# GUI相关导入
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    print("警告: tkinter不可用，GUI功能将被禁用")

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import network
    from datasets import AI4Mars, AI4MarsSMI, LabelMars6
    from utils import ext_transforms as et
    from backboned_unet import Unet
    PROJECT_AVAILABLE = True
except ImportError as e:
    PROJECT_AVAILABLE = False
    print(f"警告: 项目模块导入失败: {e}")

# ============================================================================
# FLOPs计算器类
# ============================================================================

class FLOPsCalculator:
    """专业的FLOPs计算器，支持多种深度学习层的精确计算"""
    
    def __init__(self):
        self.total_flops = 0
        self.layer_flops = {}
        self.hooks = []
        
    def calculate_flops(self, model: nn.Module, input_shape: Tuple[int, ...], 
                       device: str = 'cpu') -> Dict[str, Any]:
        """计算模型的FLOPs"""
        self.total_flops = 0
        self.layer_flops = {}
        self.hooks = []
        
        # 创建输入张量
        dummy_input = torch.randn(*input_shape).to(device)
        model = model.to(device)
        model.eval()
        
        # 注册钩子函数
        self._register_hooks(model)
        
        # 前向传播
        with torch.no_grad():
            _ = model(dummy_input)
        
        # 移除钩子
        self._remove_hooks()
        
        return {
            'total_flops': self.total_flops,
            'total_flops_G': self.total_flops / 1e9,
            'layer_flops': self.layer_flops,
            'input_shape': input_shape
        }
    
    def _register_hooks(self, model: nn.Module):
        """注册钩子函数到所有层"""
        for name, module in model.named_modules():
            if len(list(module.children())) == 0:  # 叶子节点
                hook = module.register_forward_hook(
                    lambda module, input, output, name=name: self._flop_count_hook(
                        module, input, output, name
                    )
                )
                self.hooks.append(hook)
    
    def _remove_hooks(self):
        """移除所有钩子"""
        for hook in self.hooks:
            hook.remove()
        self.hooks = []
    
    def _flop_count_hook(self, module: nn.Module, input: Tuple, output: torch.Tensor, name: str):
        """计算单个层的FLOPs"""
        flops = 0
        
        if isinstance(module, nn.Conv2d):
            flops = self._conv2d_flops(module, input[0], output)
        elif isinstance(module, nn.ConvTranspose2d):
            flops = self._conv_transpose2d_flops(module, input[0], output)
        elif isinstance(module, nn.BatchNorm2d):
            flops = self._batchnorm2d_flops(module, input[0], output)
        elif isinstance(module, (nn.ReLU, nn.ReLU6)):
            flops = self._relu_flops(module, input[0], output)
        elif isinstance(module, (nn.MaxPool2d, nn.AvgPool2d)):
            flops = self._pool2d_flops(module, input[0], output)
        elif isinstance(module, nn.AdaptiveAvgPool2d):
            flops = self._adaptive_pool2d_flops(module, input[0], output)
        elif isinstance(module, nn.Linear):
            flops = self._linear_flops(module, input[0], output)
        elif isinstance(module, nn.Upsample):
            flops = self._upsample_flops(module, input[0], output)
        elif hasattr(module, 'weight') and hasattr(module, 'bias'):
            flops = self._generic_flops(module, input[0], output)
        
        self.total_flops += flops
        self.layer_flops[name] = flops
    
    def _conv2d_flops(self, module: nn.Conv2d, input_tensor: torch.Tensor, 
                     output_tensor: torch.Tensor) -> int:
        """计算Conv2d层的FLOPs"""
        batch_size = input_tensor.shape[0]
        output_dims = output_tensor.shape[2:]
        kernel_dims = module.kernel_size
        in_channels = module.in_channels
        out_channels = module.out_channels
        groups = module.groups
        
        filters_per_channel = out_channels // groups
        conv_per_position_flops = int(np.prod(kernel_dims)) * in_channels // groups
        
        active_elements_count = batch_size * int(np.prod(output_dims))
        overall_conv_flops = conv_per_position_flops * active_elements_count * filters_per_channel
        
        bias_flops = 0
        if module.bias is not None:
            bias_flops = out_channels * active_elements_count
        
        return overall_conv_flops + bias_flops
    
    def _conv_transpose2d_flops(self, module: nn.ConvTranspose2d, input_tensor: torch.Tensor,
                               output_tensor: torch.Tensor) -> int:
        """计算ConvTranspose2d层的FLOPs"""
        batch_size = input_tensor.shape[0]
        input_dims = input_tensor.shape[2:]
        kernel_dims = module.kernel_size
        in_channels = module.in_channels
        out_channels = module.out_channels
        groups = module.groups
        
        filters_per_channel = in_channels // groups
        conv_per_position_flops = int(np.prod(kernel_dims)) * out_channels // groups
        
        active_elements_count = batch_size * int(np.prod(input_dims))
        overall_conv_flops = conv_per_position_flops * active_elements_count * filters_per_channel
        
        bias_flops = 0
        if module.bias is not None:
            bias_flops = out_channels * batch_size * int(np.prod(output_tensor.shape[2:]))
        
        return overall_conv_flops + bias_flops
    
    def _batchnorm2d_flops(self, module: nn.BatchNorm2d, input_tensor: torch.Tensor,
                          output_tensor: torch.Tensor) -> int:
        """计算BatchNorm2d层的FLOPs"""
        return input_tensor.numel() * 2
    
    def _relu_flops(self, module: nn.Module, input_tensor: torch.Tensor,
                   output_tensor: torch.Tensor) -> int:
        """计算ReLU层的FLOPs"""
        return input_tensor.numel()
    
    def _pool2d_flops(self, module: nn.Module, input_tensor: torch.Tensor,
                     output_tensor: torch.Tensor) -> int:
        """计算池化层的FLOPs"""
        if isinstance(module, nn.MaxPool2d):
            kernel_flops = int(np.prod(module.kernel_size))
        else:  # AvgPool2d
            kernel_flops = int(np.prod(module.kernel_size)) + 1
        
        return output_tensor.numel() * kernel_flops
    
    def _adaptive_pool2d_flops(self, module: nn.AdaptiveAvgPool2d, input_tensor: torch.Tensor,
                              output_tensor: torch.Tensor) -> int:
        """计算自适应池化层的FLOPs"""
        input_size = int(np.prod(input_tensor.shape[2:]))
        output_size = int(np.prod(output_tensor.shape[2:]))
        
        if output_size == 0:
            return 0
        
        kernel_flops = input_size // output_size + 1
        return output_tensor.numel() * kernel_flops
    
    def _linear_flops(self, module: nn.Linear, input_tensor: torch.Tensor,
                     output_tensor: torch.Tensor) -> int:
        """计算Linear层的FLOPs"""
        batch_size = input_tensor.shape[0]
        flops = batch_size * module.in_features * module.out_features
        
        if module.bias is not None:
            flops += batch_size * module.out_features
        
        return flops
    
    def _upsample_flops(self, module: nn.Upsample, input_tensor: torch.Tensor,
                       output_tensor: torch.Tensor) -> int:
        """计算上采样层的FLOPs"""
        if module.mode == 'nearest':
            return 0
        elif module.mode == 'linear' or module.mode == 'bilinear':
            return output_tensor.numel() * 7
        else:
            return output_tensor.numel() * 4
    
    def _generic_flops(self, module: nn.Module, input_tensor: torch.Tensor,
                      output_tensor: torch.Tensor) -> int:
        """通用FLOPs计算方法"""
        return output_tensor.numel()

# ============================================================================
# 核心评估器类
# ============================================================================

class ModelEvaluator:
    """模型效率评估器核心类"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.results = []
        
    def get_available_models(self):
        """获取可用模型列表"""
        if not PROJECT_AVAILABLE:
            return []
            
        models = []
        
        # 获取network.modeling中的模型
        for name in dir(network.modeling):
            if (name.islower() and 
                not name.startswith("_") and 
                callable(getattr(network.modeling, name))):
                models.append(name)
        
        # 添加U-Net模型
        models.extend(["unet_resnet50", "unet_resnet101"])
        
        return sorted(models)
    
    def get_num_classes(self, dataset_name):
        """根据数据集名称获取类别数"""
        dataset_classes = {
            "AI4Mars": 5,
            "AI4Mars-SMI": 5,
            "LabelMars6": 6
        }
        return dataset_classes.get(dataset_name, 5)
        
    def create_model(self, model_name, num_classes=5):
        """创建模型实例"""
        if not PROJECT_AVAILABLE:
            raise Exception("项目模块不可用")
            
        try:
            if model_name.startswith("unet_"):
                backbone_name = model_name.split("_")[1]
                model = Unet(backbone_name=backbone_name, classes=num_classes)
            else:
                model_func = getattr(network.modeling, model_name)
                model = model_func(num_classes=num_classes, output_stride=16)
            
            return model.to(self.device)
        except Exception as e:
            raise Exception(f"创建模型失败: {str(e)}")
            
    def count_parameters(self, model):
        """计算模型参数数量"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
        
    def calculate_flops(self, model, input_size, batch_size):
        """计算FLOPs"""
        try:
            calculator = FLOPsCalculator()
            input_shape = (batch_size, 3, input_size, input_size)
            result = calculator.calculate_flops(model, input_shape, str(self.device))
            return result['total_flops']
        except Exception as e:
            print(f"FLOPs计算错误: {e}")
            return 0
            
    def measure_fps(self, model, input_size, batch_size, num_iterations=100):
        """测量模型推理速度(FPS)"""
        try:
            model.eval()
            dummy_input = torch.randn(batch_size, 3, input_size, input_size).to(self.device)
            
            # 预热GPU
            with torch.no_grad():
                for _ in range(10):
                    _ = model(dummy_input)
            
            # 同步GPU
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
            
            # 测量推理时间
            start_time = time.time()
            with torch.no_grad():
                for _ in range(num_iterations):
                    _ = model(dummy_input)
            
            # 同步GPU
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
                
            end_time = time.time()
            
            total_time = end_time - start_time
            fps = (num_iterations * batch_size) / total_time
            
            return fps
            
        except Exception as e:
            print(f"FPS测试错误: {e}")
            return 0
            
    def get_hardware_info(self):
        """获取硬件环境信息"""
        info = []
        
        # 系统信息
        info.append(f"操作系统: {platform.system()} {platform.release()}")
        info.append(f"处理器: {platform.processor()}")
        
        # 内存信息
        memory = psutil.virtual_memory()
        info.append(f"总内存: {memory.total / (1024**3):.1f} GB")
        info.append(f"可用内存: {memory.available / (1024**3):.1f} GB")
        
        # GPU信息
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            info.append(f"GPU: {gpu_name}")
            info.append(f"GPU内存: {gpu_memory:.1f} GB")
        else:
            info.append("GPU: 未检测到CUDA设备")
            
        # PyTorch版本
        info.append(f"PyTorch版本: {torch.__version__}")
        
        return info

    def evaluate_model(self, model_name, input_size=512, batch_size=4, num_classes=5, progress_callback=None):
        """评估单个模型"""
        if progress_callback:
            progress_callback(10, f"正在创建模型: {model_name}")

        # 创建模型
        model = self.create_model(model_name, num_classes)

        try:
            if progress_callback:
                progress_callback(20, "正在计算参数数量...")

            # 计算参数数量
            params = self.count_parameters(model)

            if progress_callback:
                progress_callback(30, "正在计算FLOPs...")

            # 计算FLOPs
            flops = self.calculate_flops(model, input_size, batch_size)

            if progress_callback:
                progress_callback(50, "正在测试FPS...")

            # 测量FPS
            fps = self.measure_fps(model, input_size, batch_size)

            if progress_callback:
                progress_callback(90, "正在生成结果...")

            result = {
                'model_name': model_name,
                'input_size': input_size,
                'batch_size': batch_size,
                'num_classes': num_classes,
                'params': params,
                'params_M': params / 1e6,
                'flops': flops,
                'flops_G': flops / 1e9,
                'fps': fps,
                'device': str(self.device),
                'timestamp': datetime.now().isoformat()
            }

            if progress_callback:
                progress_callback(100, "评估完成")

            return result

        except Exception as e:
            if progress_callback:
                progress_callback(0, f"评估失败: {str(e)}")
            raise e
        finally:
            # 清理内存
            del model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

# ============================================================================
# GUI界面类
# ============================================================================

class ModelEvaluatorGUI:
    """GUI界面评估器"""

    def __init__(self, root):
        if not GUI_AVAILABLE:
            raise Exception("GUI不可用，请安装tkinter")

        self.root = root
        self.root.title("火星地形分割模型效率评估器")
        self.root.geometry("800x700")

        # 初始化变量
        self.evaluator = ModelEvaluator()
        self.evaluation_running = False

        # 创建GUI界面
        self.create_widgets()

        # 获取可用模型列表
        self.get_available_models()

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="模型效率评估器",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 模型选择
        ttk.Label(main_frame, text="选择模型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(main_frame, textvariable=self.model_var,
                                       state="readonly", width=40)
        self.model_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)

        # 数据集选择
        ttk.Label(main_frame, text="选择数据集:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.dataset_var = tk.StringVar(value="AI4Mars")
        dataset_combo = ttk.Combobox(main_frame, textvariable=self.dataset_var,
                                    values=["AI4Mars", "AI4Mars-SMI", "LabelMars6"],
                                    state="readonly", width=40)
        dataset_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)

        # 输入尺寸选择
        ttk.Label(main_frame, text="输入尺寸:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.input_size_var = tk.StringVar(value="512")
        input_size_combo = ttk.Combobox(main_frame, textvariable=self.input_size_var,
                                       values=["256", "512", "768", "1024"],
                                       state="readonly", width=40)
        input_size_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)

        # 批次大小选择
        ttk.Label(main_frame, text="批次大小:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.batch_size_var = tk.StringVar(value="4")
        batch_size_combo = ttk.Combobox(main_frame, textvariable=self.batch_size_var,
                                       values=["1", "2", "4", "8", "16"],
                                       state="readonly", width=40)
        batch_size_combo.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)

        # 评估按钮
        self.eval_button = ttk.Button(main_frame, text="开始评估",
                                     command=self.start_evaluation)
        self.eval_button.grid(row=5, column=0, columnspan=2, pady=20)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 状态标签
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=7, column=0, columnspan=2, pady=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="评估结果", padding="10")
        result_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S),
                         pady=10)
        result_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(8, weight=1)

        self.result_text = scrolledtext.ScrolledText(result_frame, height=15, width=70)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def get_available_models(self):
        """获取可用的模型列表"""
        try:
            available_models = self.evaluator.get_available_models()
            self.model_combo['values'] = available_models
            if available_models:
                self.model_var.set(available_models[0])
        except Exception as e:
            messagebox.showerror("错误", f"获取模型列表失败: {str(e)}")

    def update_progress(self, value, status):
        """更新进度条和状态"""
        self.progress_var.set(value)
        self.status_var.set(status)
        self.root.update_idletasks()

    def start_evaluation(self):
        """开始评估（在新线程中运行）"""
        if self.evaluation_running:
            return

        # 检查参数
        if not self.model_var.get():
            messagebox.showerror("错误", "请选择一个模型")
            return

        self.evaluation_running = True
        self.eval_button.config(state="disabled")
        self.result_text.delete(1.0, tk.END)

        # 在新线程中运行评估
        thread = threading.Thread(target=self.run_evaluation)
        thread.daemon = True
        thread.start()

    def run_evaluation(self):
        """运行模型评估"""
        try:
            # 获取参数
            model_name = self.model_var.get()
            dataset_name = self.dataset_var.get()
            input_size = int(self.input_size_var.get())
            batch_size = int(self.batch_size_var.get())
            num_classes = self.evaluator.get_num_classes(dataset_name)

            # 评估模型
            result = self.evaluator.evaluate_model(
                model_name, input_size, batch_size, num_classes,
                progress_callback=self.update_progress
            )

            # 获取硬件信息
            hardware_info = self.evaluator.get_hardware_info()

            # 生成结果报告
            self.generate_report(model_name, dataset_name, input_size, batch_size,
                               result, hardware_info)

        except Exception as e:
            messagebox.showerror("错误", f"评估过程中出现错误: {str(e)}")
            self.update_progress(0, "评估失败")
        finally:
            self.evaluation_running = False
            self.eval_button.config(state="normal")

    def generate_report(self, model_name, dataset_name, input_size, batch_size,
                       result, hardware_info):
        """生成评估报告"""
        report = []

        # 标题
        report.append("=" * 60)
        report.append("火星地形分割模型效率评估报告")
        report.append("=" * 60)
        report.append("")

        # 评估配置
        report.append("评估配置:")
        report.append(f"  模型: {model_name}")
        report.append(f"  数据集: {dataset_name}")
        report.append(f"  输入尺寸: {input_size}×{input_size}")
        report.append(f"  批次大小: {batch_size}")
        report.append(f"  类别数量: {result['num_classes']}")
        report.append("")

        # 硬件环境
        report.append("硬件环境:")
        for info in hardware_info:
            report.append(f"  {info}")
        report.append("")

        # 评估结果
        report.append("评估结果:")
        report.append(f"  参数数量 (Params): {result['params_M']:.2f} M")
        report.append(f"  浮点运算数 (FLOPs): {result['flops_G']:.2f} G")
        report.append(f"  推理速度 (FPS): {result['fps']:.1f}")
        report.append("")

        # 效率分析
        report.append("效率分析:")
        if result['params'] < 1e6:
            report.append("  ✓ 轻量级模型 (参数 < 1M)")
        elif result['params'] < 10e6:
            report.append("  ○ 中等规模模型 (参数 1M-10M)")
        else:
            report.append("  ✗ 大型模型 (参数 > 10M)")

        if result['flops'] < 5e9:
            report.append("  ✓ 低计算复杂度 (FLOPs < 5G)")
        elif result['flops'] < 50e9:
            report.append("  ○ 中等计算复杂度 (FLOPs 5G-50G)")
        else:
            report.append("  ✗ 高计算复杂度 (FLOPs > 50G)")

        if result['fps'] > 100:
            report.append("  ✓ 高推理速度 (FPS > 100)")
        elif result['fps'] > 30:
            report.append("  ○ 中等推理速度 (FPS 30-100)")
        else:
            report.append("  ✗ 低推理速度 (FPS < 30)")

        report.append("")
        report.append(f"评估时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)

        # 显示报告
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "\n".join(report))

# ============================================================================
# 批量评估器类
# ============================================================================

class BatchEvaluator:
    """批量模型评估器"""

    def __init__(self):
        self.evaluator = ModelEvaluator()
        self.results = []

    def batch_evaluate(self, model_list=None, input_size=512, batch_size=4, num_classes=5):
        """批量评估模型"""
        if model_list is None:
            model_list = self.evaluator.get_available_models()

        print(f"开始批量评估 {len(model_list)} 个模型")
        print(f"配置: 输入尺寸={input_size}, 批次大小={batch_size}, 类别数={num_classes}")
        print(f"设备: {self.evaluator.device}")
        print("-" * 60)

        self.results = []

        for i, model_name in enumerate(model_list, 1):
            print(f"[{i}/{len(model_list)}] 正在评估模型: {model_name}")

            try:
                result = self.evaluator.evaluate_model(model_name, input_size, batch_size, num_classes)
                if result:
                    self.results.append(result)
                    print(f"  参数: {result['params_M']:.2f}M, FLOPs: {result['flops_G']:.2f}G, FPS: {result['fps']:.1f}")
            except Exception as e:
                print(f"  评估失败: {e}")

            print()

        print("批量评估完成!")
        return self.results

    def save_results(self, output_dir="evaluation_results"):
        """保存评估结果"""
        if not self.results:
            print("没有评估结果可保存")
            return

        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存JSON格式
        json_file = os.path.join(output_dir, f"evaluation_results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到: {json_file}")

        # 保存CSV格式
        csv_file = os.path.join(output_dir, f"evaluation_results_{timestamp}.csv")
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if self.results:
                writer = csv.DictWriter(f, fieldnames=self.results[0].keys())
                writer.writeheader()
                writer.writerows(self.results)
        print(f"结果已保存到: {csv_file}")

        # 生成对比报告
        self.generate_comparison_report(output_dir, timestamp)

    def generate_comparison_report(self, output_dir, timestamp):
        """生成对比报告"""
        if not self.results:
            return

        report_file = os.path.join(output_dir, f"comparison_report_{timestamp}.md")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 模型效率对比报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 硬件信息
            f.write("## 硬件环境\n\n")
            hardware_info = self.evaluator.get_hardware_info()
            for info in hardware_info:
                f.write(f"- {info}\n")
            f.write("\n")

            # 评估配置
            if self.results:
                sample = self.results[0]
                f.write("## 评估配置\n\n")
                f.write(f"- 输入尺寸: {sample['input_size']}×{sample['input_size']}\n")
                f.write(f"- 批次大小: {sample['batch_size']}\n")
                f.write(f"- 类别数量: {sample['num_classes']}\n\n")

            # 结果表格
            f.write("## 评估结果\n\n")
            f.write("| 模型 | 参数(M) | FLOPs(G) | FPS | 效率评级 |\n")
            f.write("|------|---------|----------|-----|----------|\n")

            # 按FPS排序
            sorted_results = sorted(self.results, key=lambda x: x['fps'], reverse=True)

            for result in sorted_results:
                # 计算效率评级
                rating = self.calculate_efficiency_rating(result)
                f.write(f"| {result['model_name']} | {result['params_M']:.2f} | "
                       f"{result['flops_G']:.2f} | {result['fps']:.1f} | {rating} |\n")

            f.write("\n## 效率分析\n\n")

            # 最佳模型分析
            best_fps = max(self.results, key=lambda x: x['fps'])
            best_params = min(self.results, key=lambda x: x['params'])
            best_flops = min(self.results, key=lambda x: x['flops'])

            f.write(f"- **最快推理**: {best_fps['model_name']} ({best_fps['fps']:.1f} FPS)\n")
            f.write(f"- **最少参数**: {best_params['model_name']} ({best_params['params_M']:.2f}M)\n")
            f.write(f"- **最低FLOPs**: {best_flops['model_name']} ({best_flops['flops_G']:.2f}G)\n")

        print(f"对比报告已保存到: {report_file}")

    def calculate_efficiency_rating(self, result):
        """计算效率评级"""
        score = 0

        # 参数评分
        if result['params_M'] < 1:
            score += 3
        elif result['params_M'] < 10:
            score += 2
        else:
            score += 1

        # FLOPs评分
        if result['flops_G'] < 5:
            score += 3
        elif result['flops_G'] < 50:
            score += 2
        else:
            score += 1

        # FPS评分
        if result['fps'] > 100:
            score += 3
        elif result['fps'] > 30:
            score += 2
        else:
            score += 1

        # 评级映射
        if score >= 8:
            return "⭐⭐⭐"
        elif score >= 6:
            return "⭐⭐"
        else:
            return "⭐"

# ============================================================================
# 测试功能
# ============================================================================

def run_tests():
    """运行测试功能"""
    print("=" * 60)
    print("模型效率评估器测试")
    print("=" * 60)

    tests = [
        ("项目结构检查", test_project_structure),
        ("GUI可用性检查", test_gui_availability),
        ("模型创建测试", test_model_creation),
        ("参数计算测试", test_parameter_counting),
        ("FLOPs计算测试", test_flops_calculation),
        ("FPS测量测试", test_fps_measurement),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        if test_func():
            passed += 1

    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过! 评估器可以正常使用")
        print("\n使用方法:")
        print("  python model_evaluator_all_in_one.py          # 启动GUI界面")
        print("  python model_evaluator_all_in_one.py --batch  # 批量评估")
    else:
        print("⚠️  部分测试失败，请检查环境配置")

    return passed == total

def test_project_structure():
    """测试项目结构"""
    required_files = [
        'network/__init__.py',
        'network/modeling.py',
        'datasets/__init__.py',
        'utils/__init__.py',
        'backboned_unet/__init__.py'
    ]

    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        print(f"  ❌ 缺少文件: {missing_files}")
        return False
    else:
        print("  ✅ 项目结构完整")
        return True

def test_gui_availability():
    """测试GUI可用性"""
    if GUI_AVAILABLE:
        print("  ✅ GUI可用")
        return True
    else:
        print("  ❌ GUI不可用，请安装tkinter")
        return False

def test_model_creation():
    """测试模型创建"""
    if not PROJECT_AVAILABLE:
        print("  ❌ 项目模块不可用")
        return False

    try:
        evaluator = ModelEvaluator()
        models = evaluator.get_available_models()

        if not models:
            print("  ❌ 没有可用模型")
            return False

        # 测试创建第一个模型
        model = evaluator.create_model(models[0], 5)
        print(f"  ✅ 模型创建成功: {models[0]}")
        del model
        return True

    except Exception as e:
        print(f"  ❌ 模型创建失败: {e}")
        return False

def test_parameter_counting():
    """测试参数计算"""
    try:
        # 创建简单模型
        model = nn.Sequential(
            nn.Conv2d(3, 64, 3),
            nn.ReLU(),
            nn.Conv2d(64, 128, 3),
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(128, 10)
        )

        evaluator = ModelEvaluator()
        params = evaluator.count_parameters(model)
        print(f"  ✅ 参数计算成功: {params:,} 个参数")
        return True

    except Exception as e:
        print(f"  ❌ 参数计算失败: {e}")
        return False

def test_flops_calculation():
    """测试FLOPs计算"""
    try:
        # 创建简单模型
        class TestModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.conv1 = nn.Conv2d(3, 32, 3, padding=1)
                self.relu = nn.ReLU()
                self.pool = nn.AdaptiveAvgPool2d((1, 1))
                self.fc = nn.Linear(32, 5)

            def forward(self, x):
                x = self.relu(self.conv1(x))
                x = self.pool(x)
                x = x.view(x.size(0), -1)
                x = self.fc(x)
                return x

        model = TestModel()
        calculator = FLOPsCalculator()

        input_shape = (1, 3, 224, 224)
        result = calculator.calculate_flops(model, input_shape, 'cpu')

        print(f"  ✅ FLOPs计算成功: {result['total_flops_G']:.2f} G")
        return True

    except Exception as e:
        print(f"  ❌ FLOPs计算失败: {e}")
        return False

def test_fps_measurement():
    """测试FPS测量"""
    try:
        # 创建简单模型
        model = nn.Sequential(
            nn.Conv2d(3, 32, 3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(32, 5)
        )

        evaluator = ModelEvaluator()
        fps = evaluator.measure_fps(model, 256, 1, 10)  # 小规模测试

        print(f"  ✅ FPS测量成功: {fps:.1f} FPS")
        return True

    except Exception as e:
        print(f"  ❌ FPS测量失败: {e}")
        return False

# ============================================================================
# 主函数
# ============================================================================

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="火星地形分割模型效率评估器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python model_evaluator_all_in_one.py                    # 启动GUI界面
  python model_evaluator_all_in_one.py --batch            # 批量评估所有模型
  python model_evaluator_all_in_one.py --test             # 运行测试
  python model_evaluator_all_in_one.py --batch --models deeplabv3plus_resnet50 segformer_b0
        """
    )

    parser.add_argument("--batch", action="store_true", help="批量评估模式")
    parser.add_argument("--test", action="store_true", help="运行测试")
    parser.add_argument("--models", nargs="+", help="要评估的模型列表")
    parser.add_argument("--input-size", type=int, default=512, help="输入图像尺寸")
    parser.add_argument("--batch-size", type=int, default=4, help="批次大小")
    parser.add_argument("--num-classes", type=int, default=5, help="类别数量")
    parser.add_argument("--output-dir", default="evaluation_results", help="输出目录")

    args = parser.parse_args()

    # 运行测试
    if args.test:
        success = run_tests()
        return 0 if success else 1

    # 批量评估模式
    if args.batch:
        if not PROJECT_AVAILABLE:
            print("❌ 项目模块不可用，无法进行批量评估")
            return 1

        evaluator = BatchEvaluator()

        # 获取模型列表
        if args.models:
            model_list = args.models
        else:
            model_list = evaluator.evaluator.get_available_models()
            print(f"将评估所有可用模型: {len(model_list)} 个")

        # 批量评估
        results = evaluator.batch_evaluate(
            model_list=model_list,
            input_size=args.input_size,
            batch_size=args.batch_size,
            num_classes=args.num_classes
        )

        # 保存结果
        evaluator.save_results(args.output_dir)

        print(f"\n评估完成! 共评估了 {len(results)} 个模型")
        return 0

    # GUI模式（默认）
    if not GUI_AVAILABLE:
        print("❌ GUI不可用，请安装tkinter或使用 --batch 模式")
        return 1

    if not PROJECT_AVAILABLE:
        print("⚠️  项目模块不可用，GUI功能可能受限")

    try:
        root = tk.Tk()
        app = ModelEvaluatorGUI(root)
        root.mainloop()
        return 0
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
